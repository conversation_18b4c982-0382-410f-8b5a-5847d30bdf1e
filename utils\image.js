const ENV = require('../config/env');

/**
 * 获取云存储文件的临时访问URL
 * @param {string} cloudPath 云存储路径
 * @returns {Promise<string>} 临时访问URL
 */
const getCloudFileURL = async (cloudPath) => {
  try {
    if (!cloudPath || !cloudPath.startsWith('cloud://')) {
      return ENV.DEFAULT_IMAGES.PLACEHOLDER;
    }
    
    const result = await wx.cloud.getTempFileURL({
      fileList: [cloudPath]
    });
    
    if (result.fileList && result.fileList.length > 0) {
      return result.fileList[0].tempFileURL;
    }
    
    return ENV.DEFAULT_IMAGES.PLACEHOLDER;
  } catch (error) {
    console.error('获取云存储文件URL失败:', error);
    return ENV.DEFAULT_IMAGES.PLACEHOLDER;
  }
};

/**
 * 生成云存储路径
 * @param {string} filename 文件名
 * @param {string} folder 文件夹类型 (BANNER, PRODUCT, DETAIL等)
 * @returns {string} 完整的云存储路径
 */
const getCloudPath = (filename, folder = 'PRODUCT') => {
  if (!filename) return null;
  if (filename.startsWith('cloud://')) return filename;
  
  const folderPath = ENV.STORAGE_PATH[folder] || ENV.STORAGE_PATH.PRODUCT;
  return `cloud://${ENV.CLOUD_ENV}.${folderPath}${filename}`;
};

/**
 * 获取默认图片路径
 * @param {string} type 图片类型
 * @returns {string} 默认图片路径
 */
const getDefaultImage = (type = 'PLACEHOLDER') => {
  return ENV.DEFAULT_IMAGES[type] || ENV.DEFAULT_IMAGES.PLACEHOLDER;
};

/**
 * 处理图片URL，支持云存储和本地图片
 * @param {string} src 图片源路径
 * @param {string} defaultType 默认图片类型
 * @returns {Promise<string>} 处理后的图片URL
 */
const processImageUrl = async (src, defaultType = 'PLACEHOLDER') => {
  if (!src) {
    return getDefaultImage(defaultType);
  }
  
  // 如果是云存储路径，获取临时URL
  if (src.startsWith('cloud://')) {
    return await getCloudFileURL(src);
  }
  
  // 如果是相对路径，直接返回
  if (src.startsWith('/') || src.startsWith('./')) {
    return src;
  }
  
  // 如果是完整URL，直接返回
  if (src.startsWith('http://') || src.startsWith('https://')) {
    return src;
  }
  
  // 其他情况返回默认图片
  return getDefaultImage(defaultType);
};

/**
 * 批量处理图片URL
 * @param {Array} imageList 图片路径数组
 * @param {string} defaultType 默认图片类型
 * @returns {Promise<Array>} 处理后的图片URL数组
 */
const processImageList = async (imageList, defaultType = 'PLACEHOLDER') => {
  if (!Array.isArray(imageList)) {
    return [];
  }
  
  const promises = imageList.map(src => processImageUrl(src, defaultType));
  return await Promise.all(promises);
};

module.exports = {
  getCloudFileURL,
  getCloudPath,
  getDefaultImage,
  processImageUrl,
  processImageList
}; 








