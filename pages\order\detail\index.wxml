<view class="container">
  <block wx:if="{{order}}">
    <!-- 订单状态 -->
    <view class="status-bar">
      <text class="status-text">{{statusText[order.status]}}</text>
      <text class="status-desc" wx:if="{{order.status === 5}}">退款申请处理中</text>
      <text class="status-desc" wx:if="{{order.status === 6}}">退款已完成</text>
    </view>

    <!-- 收货地址 -->
    <view class="address-section">
      <view class="address-info">
        <view class="contact">
          <text class="name">{{order.address.name}}</text>
          <text class="phone">{{order.address.phone}}</text>
        </view>
        <view class="address">{{order.address.province}}{{order.address.city}}{{order.address.district}}{{order.address.detail}}</view>
      </view>
    </view>

    <!-- 商品列表 -->
    <view class="product-list">
      <view class="product-item" wx:for="{{order.products}}" wx:key="productId">
        <image class="product-image" src="{{item.image}}" mode="aspectFill"/>
        <view class="product-info">
          <view class="product-name">{{item.name}}</view>
          <view class="product-spec" wx:if="{{item.specs}}">{{item.specs.name}}</view>
          <view class="price-quantity">
            <text class="price">¥{{item.price}}</text>
            <text class="quantity">x{{item.quantity}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="order-info">
      <view class="info-item">
        <text class="label">订单编号</text>
        <text class="value">{{order.orderNo}}</text>
      </view>
      <view class="info-item">
        <text class="label">创建时间</text>
        <text class="value">{{order.createTime}}</text>
      </view>
      <view class="info-item" wx:if="{{order.payTime}}">
        <text class="label">支付时间</text>
        <text class="value">{{order.payTime}}</text>
      </view>
    </view>

    <!-- 价格信息 -->
    <view class="price-section">
      <view class="price-item">
        <text>商品总价</text>
        <text>¥{{order.totalAmount}}</text>
      </view>
      <!-- 优惠信息 -->
      <block wx:if="{{order.discount}}">
        <view class="price-item discount">
          <text>{{order.discount.name}}</text>
          <text class="discount-value">-¥{{order.discount.amount}}</text>
        </view>
      </block>
      <!-- 优惠明细 -->
      <block wx:elif="{{order.discountDetails && order.discountDetails.length > 0}}">
        <view class="price-item discount" wx:for="{{order.discountDetails}}" wx:key="name">
          <text>{{item.name}}</text>
          <text class="discount-value">-¥{{item.discount}}</text>
        </view>
      </block>
      <!-- 运费 -->
      <view class="price-item">
        <text>运费</text>
        <text>包邮</text>
      </view>
      <!-- 实付款 -->
      <view class="price-item final">
        <text>实付款</text>
        <text class="final-price">¥{{order.finalAmount || order.totalAmount}}</text>
      </view>
    </view>
  </block>
</view> 