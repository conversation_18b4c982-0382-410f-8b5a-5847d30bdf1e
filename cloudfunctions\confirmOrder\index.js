const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})
const db = cloud.database()

exports.main = async (event, context) => {
  try {
    const { orderId, type } = event
    const reason = type === 'auto' ? '七天自动确认收货' : '用户手动确认收货'
    
    return await db.collection('orders').doc(orderId).update({
      data: {
        status: 3,  // 已完成
        confirmTime: db.serverDate(),
        confirmReason: reason
      }
    })
  } catch (err) {
    console.error(err)
    return err
  }
} 