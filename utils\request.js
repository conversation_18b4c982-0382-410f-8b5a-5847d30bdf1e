// 基础配置
const BASE_URL = 'https://your-api-domain.com';
const DEFAULT_HEADER = {
  'Content-Type': 'application/json'
};

// 统一的请求工具
const request = {
  // 基础请求方法
  baseRequest: function(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: BASE_URL + options.url,
        method: options.method || 'GET',
        data: options.data,
        header: { ...DEFAULT_HEADER, ...options.header },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(res);
          }
        },
        fail: reject
      });
    });
  },

  get: function(url, params = {}) {
    return this.baseRequest({
      url,
      method: 'GET',
      data: params
    });
  },

  post: function(url, data = {}) {
    return this.baseRequest({
      url,
      method: 'POST',
      data
    });
  }
};

export default request; 