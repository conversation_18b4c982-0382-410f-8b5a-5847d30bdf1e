.container {
  padding: 20rpx;
  min-height: 100vh;
  background: #f8f8f8;
}

.address-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.info {
  flex: 1;
  margin-right: 20rpx;
}

.top {
  margin-bottom: 10rpx;
}

.name {
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.default {
  font-size: 24rpx;
  color: #ff4d4f;
  border: 1rpx solid #ff4d4f;
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  margin-left: 20rpx;
}

.address {
  font-size: 26rpx;
  color: #333;
}

.edit {
  font-size: 26rpx;
  color: #666;
  padding: 10rpx 20rpx;
}

.empty {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: #fff;
}

.add-btn {
  background: #987;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.edit, .delete {
  font-size: 26rpx;
  padding: 10rpx 20rpx;
}

.delete {
  color: #ff4d4f;
} 