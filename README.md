# 小程序云开发配置指南

## 项目概述

这是一个基于微信小程序云开发的项目，用于管理商品、订单、会员等功能。

## 环境配置

### 1. 设置云环境ID

所有云环境ID都集中在 `config/env.js` 文件中配置：

```javascript
const ENV = {
  // 云开发环境ID
  CLOUD_ENV: 'cloud1-2gfror0a1b871162',
  
  // 小程序APPID
  APP_ID: 'wxf57755081128c53d',
  
  // API基础URL
  BASE_URL: 'https://your-api-domain.com',
  
  // 图片存储路径配置
  STORAGE_PATH: {
    BANNER: 'images/轮播/',
    AVATAR: 'images/默认头像.png',
    PRODUCT: 'products/',
    DETAIL: 'products/detail/'
  },
  
  // 默认图片配置
  DEFAULT_IMAGES: {
    BANNER: '/images/logo.png',
    AVATAR: '/images/默认头像.png',
    PRODUCT: '/images/default-product.png',
    PLACEHOLDER: '/images/placeholder.png'
  },
  
  // 开发模式配置
  DEBUG: true
};
```

**新开发者请根据自己的云环境修改 `CLOUD_ENV` 的值。**

### 2. 云函数配置

云函数已配置为使用 `DYNAMIC_CURRENT_ENV` 环境变量，无需手动修改。

### 3. 图片路径处理

项目使用了统一的图片路径处理工具函数：

- `getCloudFileURL`: 获取云存储图片的临时访问URL
- `getCloudPath`: 生成完整的云存储路径
- `getDefaultImage`: 获取默认图片路径

示例用法：

```javascript
const { getCloudFileURL, getCloudPath } = require('../../utils/image');

// 获取轮播图临时URL
const imageUrl = await getCloudFileURL(getCloudPath('轮播1.jpg', 'BANNER'));
```

## 开发规范

1. **图片路径**：不要硬编码完整的云存储路径，使用 `getCloudPath` 函数生成
2. **配置管理**：环境相关的配置都应该放在 `config/env.js` 文件中
3. **错误处理**：所有网络请求和图片加载应该有适当的错误处理
4. **日志记录**：使用一致的日志格式方便调试

## 常见问题

1. **图片加载失败**：检查云环境ID是否正确，图片路径是否存在
2. **云函数调用失败**：检查云环境初始化是否正确，环境ID是否匹配
3. **多账号开发**：每个开发者需要修改 `config/env.js` 中的 `CLOUD_ENV` 为自己的云环境ID 