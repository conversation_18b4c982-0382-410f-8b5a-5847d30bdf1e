/**app.wxss**/
.container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
} 

/* 全局页面背景 */
page {
  background-color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* 通用布局类 */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

/* 产品卡片样式 */
.product-card {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
}

.product-image {
  width: 100%;
  height: 300rpx;
  object-fit: cover;
  display: block;
}

.product-info {
  padding: 20rpx;
}

.product-name {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 10rpx;
  font-weight: bold;
}

.product-price {
  font-size: 36rpx;
  color: #A67B5B;
  font-weight: bold;
}

.product-desc {
  font-size: 28rpx;
  color: #666666;
  margin-top: 10rpx;
}

/* 按钮样式 */
.btn-primary {
  background: #A67B5B;
  color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx 30rpx;
  font-size: 32rpx;
  border: none;
}

.btn-secondary {
  background: #f8f8f8;
  color: #333333;
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  padding: 20rpx 30rpx;
  font-size: 32rpx;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  background: #f8f8f8;
  padding-bottom: 120rpx;
}

.loading-container {
  text-align: center;
  padding: 100rpx 0;
  color: #999999;
}






