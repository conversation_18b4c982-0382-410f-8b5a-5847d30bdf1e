const cache = {
  data: new Map(),
  
  set: (key, value, expireSeconds = 300) => {
    const expireTime = Date.now() + expireSeconds * 1000;
    cache.data.set(key, {
      value,
      expireTime
    });
  },

  get: (key) => {
    const item = cache.data.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expireTime) {
      cache.data.delete(key);
      return null;
    }
    
    return item.value;
  },

  clear: () => {
    cache.data.clear();
  }
};

module.exports = cache; 