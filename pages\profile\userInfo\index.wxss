.container {
  padding: 30rpx;
  background: #f8f8f8;
  min-height: 100vh;
}

.info-list {
  background: #fff;
  border-radius: 12rpx;
  padding: 0 30rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  color: #333;
  font-size: 28rpx;
}

.value {
  color: #666;
  font-size: 28rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  background: #f5f5f5;
}

.avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.info-item .value.user-id {
  color: #987;  /* 使用主题色 */
  font-family: Consolas, monospace;  /* 使用等宽字体 */
  letter-spacing: 1rpx;  /* 增加字间距 */
}

.right-content {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.arrow {
  color: #999;
  font-size: 24rpx;
}

/* 可编辑项的样式 */
.info-item.editable {
  cursor: pointer;
}

.info-item.editable:active {
  background: rgba(0, 0, 0, 0.05);
} 