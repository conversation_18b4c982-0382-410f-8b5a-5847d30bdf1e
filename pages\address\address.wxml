<view class="container">
  <!-- 地址列表 -->
  <view class="address-list" wx:if="{{addressList.length > 0}}">
    <view class="address-item" wx:for="{{addressList}}" wx:key="_id" bindtap="selectAddress" data-id="{{item._id}}">
      <view class="info">
        <view class="top">
          <text class="name">{{item.name}}</text>
          <text class="phone">{{item.phone}}</text>
          <text class="default" wx:if="{{item.isDefault}}">默认</text>
        </view>
        <view class="address">{{item.province}}{{item.city}}{{item.district}}{{item.detail}}</view>
      </view>
      <view class="actions">
        <view class="edit" catchtap="editAddress" data-id="{{item._id}}">编辑</view>
        <view class="delete" catchtap="deleteAddress" data-id="{{item._id}}">删除</view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty" wx:else>
    <text>暂无收货地址</text>
  </view>

  <!-- 底部按钮 -->
  <view class="footer">
    <button class="add-btn" bindtap="createAddress">+ 新建收货地址</button>
  </view>
</view> 