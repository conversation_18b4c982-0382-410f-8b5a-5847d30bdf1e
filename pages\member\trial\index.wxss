.container {
  min-height: 100vh;
  background: #f8f8f8;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: env(safe-area-inset-top) 0 env(safe-area-inset-bottom);
  box-sizing: border-box;
}

.welcome-animation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(184, 130, 69, 0.897);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  pointer-events: none;
  transform: scale(1.1);
  transition: all 0.5s ease;
  z-index: 9999;
}

.welcome-animation.show {
  opacity: 1;
  pointer-events: auto;
}

.welcome-box {
  position: relative;
  width: 100vw;
  height: 100vh;
}

.welcome-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: all 0.5s ease;
}

.welcome-text.hi {
  color: #000;
  font-size: 180rpx;
  font-family: "FZShouJinShu-Z01S";
  opacity: 0;
  transition: all 0.5s ease;
}

.welcome-text.message {
  width: 100%;
  text-align: center;
}

.message-content {
  color: rgb(0, 0, 0);
  font-size: 70rpx;
  font-family: "FZShouJinShu-Z01S";
  white-space: nowrap;
  transform: translate(-50%, -50%) scale(0.8);
  display: inline-block;
  position: absolute;
  left: 50%;
  top: 50%;
}

.welcome-animation.show .welcome-text.hi {
  opacity: 1;
}

.welcome-text.hi.hide {
  opacity: 0 !important;
}

.welcome-text.message.show {
  opacity: 1;
  transform: translate(-50%, -50%);
}

.content {
  width: 100%;
  min-height: 100vh;
  position: relative;
  opacity: 0;
  transform: translateY(30rpx);
  transition: all 0.5s ease;
  background: #f8f8f8;
  z-index: 95;
  max-width: 750rpx;
  margin: 0 auto;
  padding: 20rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.content.show {
  opacity: 1;
  transform: translateY(0);
}

.content.hidden {
  visibility: hidden;
  pointer-events: none;
}

.trial-header {
  position: relative;
  z-index: 1;
}

.trial-form {
  padding: 0;
  margin-top: 0;
  width: 100%;
}

.form-item {
  margin-bottom: 30rpx;
  position: relative;
  z-index: 5;
  width: 100%;
}

.label {
  font-size: 28rpx;
  color: #987;
  font-weight: bold;
  margin-bottom: 12rpx;
  display: block;
}

.input {
  background: rgba(152, 136, 119, 0.05);
  border-radius: 12rpx;
  min-height: 88rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  width: 100%;
  position: relative;
  z-index: 100;
  box-sizing: border-box;
}

.upload-area {
  background: rgba(152, 136, 119, 0.05);
  border-radius: 12rpx;
  min-height: 200rpx;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
}

.upload-icon {
  font-size: 48rpx;
  color: #987;
  margin-bottom: 12rpx;
}

.upload-text {
  font-size: 26rpx;
  color: #987;
  opacity: 0.8;
}

.preview-area {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
  background: #fff;
  border-radius: 8rpx;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.delete-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 44rpx;
  height: 44rpx;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.submit-btn {
  width: 90%;
  margin: 40rpx auto;
  height: 88rpx;
  background: #987;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(152, 136, 119, 0.2);
}

.submit-btn:active {
  opacity: 0.8;
  transform: translateY(2rpx);
}

.agreement-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.agreement-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.6);
}

.modal-content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: min(80%, 600rpx);
  height: 80vh;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 20rpx 30rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  border-bottom: 1rpx solid #eee;
  position: relative;
  flex: none;
}

.close {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40rpx;
  color: #999;
}

.modal-body {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  -webkit-overflow-scrolling: touch;
  position: relative;
  height: 0;
}

.agreement-image {
  width: 90%;
  height: auto;
  object-fit: contain;
  margin-bottom: 20rpx;
  flex-shrink: 0;
  display: block;
}

.modal-footer {
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  border-top: 1rpx solid #eee;
  flex-shrink: 0;
  background: #fff;
  position: relative;
  z-index: 1;
  flex: none;
}

.disagree-btn, .agree-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0;
  padding: 0;
}

.disagree-btn {
  background: #f5f5f5;
  color: #666;
}

.agree-btn {
  background: #987;
  color: #fff;
}

/* 添加协议勾选相关样式 */
.agreement-check {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  margin-top: 40rpx;
}

.checkbox {
  flex-shrink: 0;
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid rgba(152, 136, 119, 0.3);
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox.checked {
  background: #987;
  border-color: #987;
}

.check-inner {
  width: 20rpx;
  height: 12rpx;
  border: 2rpx solid #fff;
  border-top: none;
  border-right: none;
  transform: rotate(-45deg);
  opacity: 0;
}

.checkbox.checked .check-inner {
  opacity: 1;
}

.agreement-text {
  font-size: 26rpx;
  color: #666;
  text-align: center;
}

.agreement-text .link {
  color: #987;
  display: inline;
}

.submit-btn.disabled {
  opacity: 0.5;
  background: #ccc;
}

/* 活动规则样式 */
.rules-section {
  width: 100%;
  max-width: 710rpx;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.5s ease;
  flex-shrink: 0;
}

.rules-section.show {
  opacity: 1;
  transform: translateY(0);
}

.rules-bg {
  background: linear-gradient(135deg, #f8f4f0 0%, #fff 100%);
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.rules-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 2rpx dashed rgba(152, 136, 119, 0.2);
  justify-content: center;
}

.rules-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.title-group {
  flex: 1;
  text-align: center;
}

.rules-header .title {
  font-size: 36rpx;
  font-weight: bold;
  color: #987;
  display: block;
  margin-bottom: 10rpx;
  text-align: center;
}

.rules-header .subtitle {
  font-size: 28rpx;
  color: #987;
  opacity: 0.8;
  text-align: center;
}

.group-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.number {
  font-size: 40rpx;
  font-weight: bold;
  color: #987;
  opacity: 0.15;
  margin-right: 16rpx;
}

.group-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #987;
  display: block;
}

.step-item {
  background: rgba(152, 136, 119, 0.05);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
}

.step-num {
  font-size: 24rpx;
  color: #987;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.step-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.notice {
  margin-top: 30rpx;
  padding: 20rpx;
  background: rgba(255, 77, 79, 0.05);
  border-radius: 8rpx;
  border: 1rpx dashed rgba(255, 77, 79, 0.3);
}

.notice-text {
  font-size: 24rpx;
  color: #ff4d4f;
  line-height: 1.6;
}

/* 表单背景样式 */
.form-bg {
  background: linear-gradient(135deg, #f8f4f0 0%, #fff 100%);
  border-radius: 24rpx;
  width: 100%;
  max-width: 710rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  flex-shrink: 0;
}

.form-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 2rpx dashed rgba(152, 136, 119, 0.2);
}

.form-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.form-header .title-group {
  flex: 1;
  text-align: center;
}

.form-header .title {
  font-size: 34rpx;
  line-height: 1.4;
  letter-spacing: 1rpx;
  font-weight: bold;
  color: #987;
  display: block;
  margin-bottom: 10rpx;
}

.form-header .subtitle {
  font-size: 28rpx;
  color: #987;
  opacity: 0.8;
}

/* 性别选择样式 */
.radio-group {
  display: flex;
  gap: 20rpx;
}

.radio-item {
  flex: 1;
  height: 88rpx;
  background: rgba(152, 136, 119, 0.05);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.radio-item.active {
  background: rgba(152, 136, 119, 0.15);
}

.radio-text {
  font-size: 28rpx;
  color: #987;
}

.radio-item.active .radio-text {
  font-weight: bold;
}

/* 响应式布局 */
@media screen and (min-width: 768px) {
  .content {
    max-width: 960rpx;
    flex-direction: row;
    align-items: flex-start;
    gap: 40rpx;
  }

  .form-bg,
  .rules-section {
    flex: 1;
    align-self: stretch;
  }
}

/* 确保标题组件占据正确的空间 */
.rules-header .title-group {
  flex: 1;
  text-align: center;
  max-width: calc(100% - 100rpx); /* 图标宽度+间距 */
} 