/**
 * 订单服务模块
 */
const orderService = {
  /**
   * 获取订单列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Array>} 订单列表
   */
  getOrderList: async function(params = {}) {
    try {
      const db = wx.cloud.database();
      const userInfo = wx.getStorageSync('userInfo');
      
      if (!userInfo || !userInfo._openid) {
        throw new Error('用户未登录');
      }
      
      // 构建查询条件
      const query = {
        _openid: userInfo._openid
      };
      
      // 添加状态筛选
      if (params.status !== undefined) {
        query.status = params.status;
      }
      
      // 查询订单
      const res = await db.collection('orders')
        .where(query)
        .orderBy('createTime', 'desc')
        .get();
      
      return res.data || [];
    } catch (err) {
      console.error('获取订单列表失败', err);
      throw err;
    }
  },
  
  /**
   * 获取订单详情
   * @param {String} orderId - 订单ID
   * @returns {Promise<Object>} 订单详情
   */
  getOrderDetail: async function(orderId) {
    try {
      const db = wx.cloud.database();
      const res = await db.collection('orders').doc(orderId).get();
      return res.data;
    } catch (err) {
      console.error('获取订单详情失败', err);
      throw err;
    }
  }
};

export default orderService; 