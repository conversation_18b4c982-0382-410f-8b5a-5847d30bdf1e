.container {
  min-height: 100vh;
  background: #f8f8f8;
  padding: 20rpx;
}

.exchange-section {
  background: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.input-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.input-box input {
  flex: 1;
  height: 80rpx;
  background: #f8f8f8;
  border-radius: 40rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

.exchange-btn {
  width: 160rpx;
  height: 80rpx;
  background: #987;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.coupon-list {
  padding: 20rpx 0;
}

.coupon-item {
  display: flex;
  background: linear-gradient(to right, #987, #b9a89f);
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  color: #fff;
  position: relative;
  overflow: hidden;
}

.coupon-item.used {
  background: #ccc;
  opacity: 0.8;
}

.left {
  width: 200rpx;
  border-right: 2rpx dashed rgba(255,255,255,0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.amount {
  display: flex;
  align-items: baseline;
}

.symbol {
  font-size: 32rpx;
}

.number {
  font-size: 60rpx;
  font-weight: bold;
  margin-left: 4rpx;
}

.condition {
  font-size: 24rpx;
  margin-top: 10rpx;
}

.right {
  flex: 1;
  padding-left: 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.date {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 10rpx;
}

.status {
  font-size: 24rpx;
  background: rgba(255,255,255,0.2);
  display: inline-block;
  padding: 4rpx 20rpx;
  border-radius: 20rpx;
}

.empty {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
} 