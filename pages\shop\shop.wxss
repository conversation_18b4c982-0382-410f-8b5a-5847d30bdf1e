/* pages/shop/shop.wxss */
.container {
  padding: var(--spacing-small);
  background: var(--bg-secondary);
  min-height: 100vh;
}

.category-nav {
  display: flex;
  background: #fff;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
}

.nav-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.nav-item.active {
  color: #333;
  font-weight: bold;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #333;
  border-radius: 2rpx;
}

/* 商品网格布局 */
.product-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-small);
  padding: var(--spacing-small) 0;
}

.product-item {
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: all var(--transition-normal);
}

.product-item:active {
  transform: scale(0.98);
}

.product-image {
  width: 100%;
  height: 300rpx;
  background: var(--bg-tertiary);
}

.product-info {
  padding: var(--spacing-small);
}

.product-name {
  font-size: var(--font-size-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.price-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.price {
  font-size: var(--font-size-large);
  color: var(--primary-color);
  font-weight: bold;
}

.original-price {
  font-size: var(--font-size-small);
  color: var(--text-tertiary);
  text-decoration: line-through;
}

.cart-btn {
  position: fixed;
  right: var(--spacing-medium);
  bottom: var(--spacing-xl);
  width: 100rpx;
  height: 100rpx;
  background: var(--primary-color);
  border-radius: var(--border-radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-medium);
  z-index: 999;
}

.cart-icon {
  width: 50rpx;
  height: 50rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  min-height: 60vh;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: var(--spacing-medium);
}

.empty-text {
  font-size: var(--font-size-large);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.empty-subtext {
  font-size: var(--font-size-small);
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-large);
}

.refresh-btn {
  background: var(--primary-color);
  color: var(--text-white);
  border-radius: var(--border-radius);
  padding: var(--spacing-small) var(--spacing-medium);
  border: none;
}
