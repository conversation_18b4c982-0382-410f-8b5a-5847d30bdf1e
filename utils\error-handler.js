const errorHandler = {
  handleError: (err, context = '') => {
    console.error(`[${context}] 错误:`, {
      message: err.message,
      stack: err.stack,
      time: new Date().toISOString()
    });

    return {
      success: false,
      error: err.message,
      code: err.code || -1
    };
  },

  throwError: (message, code = -1) => {
    const error = new Error(message);
    error.code = code;
    throw error;
  }
};

module.exports = errorHandler; 