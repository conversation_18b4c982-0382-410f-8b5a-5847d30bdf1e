Component({
  properties: {
    src: {
      type: String,
      value: ''
    },
    mode: {
      type: String,
      value: 'aspectFill'
    },
    customClass: {
      type: String,
      value: ''
    }
  },

  data: {
    currentSrc: '',
    loadError: false
  },

  lifetimes: {
    attached() {
      console.log('[通用图片组件v2] 组件初始化');
      this.updateSrc();
    }
  },

  observers: {
    'src': function(newSrc) {
      console.log('[通用图片组件v2] src变化:', newSrc);
      this.updateSrc();
    }
  },

  methods: {
    updateSrc() {
      const src = this.data.src || '/images/logo.png';
      this.setData({
        currentSrc: src,
        loadError: false
      });
    },

    onImageError(e) {
      console.warn('[通用图片组件v2] 图片加载失败:', e.detail);
      
      if (!this.data.loadError) {
        this.setData({
          currentSrc: '/images/logo.png',
          loadError: true
        });
      }
      
      this.triggerEvent('error', e.detail);
    },

    onImageLoad(e) {
      console.log('[通用图片组件v2] 图片加载成功');
      this.triggerEvent('load', e.detail);
    }
  }
})