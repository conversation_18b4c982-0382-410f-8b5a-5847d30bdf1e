/* pages/profile/profile.wxss */
.container {
  min-height: 100vh;
  background: #f8f8f8;
}

.user-section {
  height: 30vh;  /* 从40vh改为30vh */
  background: linear-gradient(to bottom, #d2b48c, #987);
  padding: 40rpx 30rpx;
  position: relative;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  position: relative;
  z-index: 2;
  padding: 10rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  backdrop-filter: blur(5px);
  cursor: pointer;  /* 添加手型光标 */
}

.avatar {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  flex-shrink: 0;
  border-radius: 50%;
  background: #fff;
  margin-right: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.avatar image {
  display: block;
  width: 120rpx;  /* 与容器大小保持一致 */
  height: 120rpx;
  border-radius: 50%;
  object-fit: cover;
}

/* 添加可点击效果 */
.avatar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.2s;
}

.avatar:active::after {
  opacity: 1;
}

.user-detail {
  flex: 1;
  color: #fff;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}

.nickname {
  font-size: 34rpx;
  font-family: "Source Han Sans CN", "思源黑体", sans-serif;
  font-weight: normal;  /* 移除加粗 */
  margin-bottom: 8rpx;
}

.user-id {
  font-size: 24rpx;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.9);
}

.vip-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  padding: 20rpx 20rpx;  /* 调整内边距 */
  position: relative;
  z-index: 2;
  backdrop-filter: blur(10px);
}

.vip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;  /* 从20rpx减小到15rpx */
}

.vip-title {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
}

.vip-level {
  background: #fff;
  color: #987;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.vip-benefits {
  display: flex;
  justify-content: space-around;  /* 改为 space-around */
  padding: 0 20rpx;  /* 添加左右内边距 */
  margin-top: 20rpx;  /* 增加与标题的间距 */
}

.benefit-item {
  text-align: center;
  color: #fff;
  flex: 1;  /* 让每个项目占据相等空间 */
  max-width: 140rpx;  /* 限制最大宽度 */
  display: flex;
  flex-direction: column;
  align-items: center;  /* 确保内容居中 */
}

.benefit-icon {
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 6rpx;
}

.benefit-name {
  font-size: 24rpx;
  white-space: nowrap;  /* 防止文字换行 */
}

.order-section {
  margin-top: -80rpx;
  background: #fff;
  padding: 30rpx;
  position: relative;
  z-index: 3;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-header text {
  font-size: 30rpx;
  font-weight: bold;
}

.view-all {
  font-size: 26rpx;
  color: #999;
}

.arrow {
  margin-left: 10rpx;
}

.order-types {
  display: flex;
  justify-content: space-between;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.type-item image {
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 10rpx;
}

.type-item text {
  font-size: 24rpx;
  color: #666;
}

.function-list {
  margin-top: 0;
  background: #fff;
  position: relative;
  z-index: 3;
}

.function-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28rpx;
  border-bottom: 1rpx solid #f5f5f5;
  font-size: 28rpx;
}

.function-item:last-child {
  border-bottom: none;
}

.function-item .arrow {
  color: #999;
}

.phone-auth-btn {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
}

.user-info-btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 200rpx !important;  /* 限制高度只覆盖用户信息区域 */
  opacity: 0;
  z-index: 3;
  margin: 0;
  padding: 0;
  border: none;
  background: none;
}

.user-info:active {
  opacity: 0.8;
}

.agreement-modal,
.service-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  z-index: 9999;
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 75%;
  max-width: 550rpx;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  z-index: 10000;
}

.modal-header {
  padding: 20rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.close {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.modal-body {
  max-height: 55vh;
  padding: 20rpx 30rpx;
}

.text-line {
  display: block;
  margin-bottom: 16rpx;
  font-size: 24rpx;
  line-height: 1.5;
  font-family: SimSun, "宋体";
  color: rgb(19, 18, 18);
}

.service-content {
  width: 65%;
  max-width: 500rpx;
}

.service-body {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qr-code {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 20rpx;
}

.service-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.service-tip {
  font-size: 24rpx;
  color: #999;
}

.work-time {
  font-size: 22rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 客服二维码弹窗样式 */
.customer-service-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 500rpx;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  padding: 20rpx;
  text-align: center;
  position: relative;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 28rpx;
  font-weight: bold;
}

.close-btn {
  position: absolute;
  right: 30rpx;
  top: 20rpx;
  font-size: 32rpx;
  color: #999;
  line-height: 1;
}

.modal-body {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qr-code {
  width: 300rpx;
  height: auto;
  margin-bottom: 20rpx;
}

.qr-tip {
  font-size: 24rpx;
  color: #666;
}