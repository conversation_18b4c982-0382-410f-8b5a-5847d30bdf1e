<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <button 
      wx:if="{{!isLogin}}" 
      class="user-info-btn"
      open-type="getPhoneNumber" 
      bindgetphonenumber="getPhoneNumber"
    ></button>
    
    <!-- 用户信息 -->
    <view class="user-info" bindtap="{{isLogin ? 'goToUserInfo' : ''}}">
      <view class="avatar">
        <image 
          class="avatar" 
          src="{{userInfo.avatarUrl || defaultAvatar}}" 
          mode="aspectFill"
        />
      </view>
      <view class="user-detail">
        <view class="nickname">{{isLogin ? userInfo.nickName : '点击登录'}}</view>
        <view class="user-id" wx:if="{{isLogin}}">Id：{{userInfo.userId}}</view>
      </view>
    </view>

    <!-- 会员信息 -->
    <view class="vip-section">
      <view class="vip-header">
        <text class="vip-title">会员特权</text>
      </view>
      <view class="vip-benefits">
        <view class="benefit-item" bindtap="goToBenefits">
          <image class="benefit-icon" src="/images/会员专属优惠.png" mode="aspectFit"/>
          <text class="benefit-name">专属优惠</text>
        </view>
        <view class="benefit-item">
          <image class="benefit-icon" src="/images/生日礼包.png" mode="aspectFit"/>
          <text class="benefit-name">生日礼包</text>
        </view>
        <view class="benefit-item" bindtap="goToTrial">
          <image class="benefit-icon" src="/images/新品试用.png" mode="aspectFit"/>
          <text class="benefit-name">新品试用</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 订单区域 -->
  <view class="order-section">
    <view class="section-header">
      <text>我的订单</text>
      <view class="view-all" bindtap="viewAllOrders">
        <text>查看全部</text>
        <text class="arrow">></text>
      </view>
    </view>
    <view class="order-types">
      <view class="type-item" bindtap="goToOrders" data-type="1">
        <image src="/images/待付款.png"></image>
        <text>待付款</text>
      </view>
      <view class="type-item" bindtap="goToOrders" data-type="2">
        <image src="/images/待发货.png"></image>
        <text>待发货</text>
      </view>
      <view class="type-item" bindtap="goToOrders" data-type="3">
        <image src="/images/首页-已发货.png"></image>
        <text>已发货</text>
      </view>
      <view class="type-item" bindtap="goToOrders" data-type="4">
        <image src="/images/退款.png"></image>
        <text>退款/售后</text>
      </view>
    </view>
  </view>

  <!-- 功能列表 -->
  <view class="function-list">
    <view class="function-item" bindtap="goToCoupons">
      <text>U粉券</text>
      <text class="arrow">></text>
    </view>
    <view class="function-item" bindtap="goToAddress">
      <text>地址管理</text>
      <text class="arrow">></text>
    </view>
    <view class="function-item" bindtap="showServiceQR">
      <text>联系客服</text>
      <text class="arrow">></text>
    </view>
    <view class="function-item" bindtap="goToAgreement">
      <text>用户协议及购买服务条款</text>
      <text class="arrow">></text>
    </view>
    <view class="function-item" bindtap="goToAbout">
      <text>关于我们</text>
      <text class="arrow">></text>
    </view>
  </view>

  <!-- 客服二维码弹窗 -->
  <view class="customer-service-modal" wx:if="{{showServiceQR}}">
    <view class="modal-mask" bindtap="hideServiceQR"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">联系客服</text>
        <view class="close-btn" bindtap="hideServiceQR">×</view>
      </view>
      <view class="modal-body">
        <image class="qr-code" src="/images/企业微信客服.png" mode="widthFix"></image>
        <text class="qr-tip">扫码添加客服微信</text>
        <text class="work-time">工作时间：10:00-20:00</text>
      </view>
    </view>
  </view>

  <!-- 用户协议模态框 -->
  <view class="agreement-modal" wx:if="{{showAgreement}}">
    <view class="modal-mask" bindtap="hideAgreement"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>用户协议及服务条款</text>
        <view class="close" bindtap="hideAgreement">×</view>
      </view>
      <scroll-view scroll-y class="modal-body">
        <text wx:for="{{agreementContent}}" wx:key="index" class="text-line">{{item}}</text>
      </scroll-view>
    </view>
  </view>

  <!-- 客服弹窗 -->
  <view class="service-modal" wx:if="{{showService}}">
    <view class="modal-mask" bindtap="hideService"></view>
    <view class="modal-content service-content">
      <view class="modal-header">
        <text>联系客服</text>
        <view class="close" bindtap="hideService">×</view>
      </view>
      <view class="service-body">
        <image class="qr-code" src="/images/客服二维码.png" mode="widthFix"></image>
        <text class="service-text">扫码添加企业微信客服</text>
        <text class="service-tip">工作时间：周一至周日 9:00-21:00</text>
      </view>
    </view>
  </view>
</view> 