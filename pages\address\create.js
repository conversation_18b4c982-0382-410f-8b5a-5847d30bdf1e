const db = wx.cloud.database()

// 示例省市区数据（实际项目中应该从服务器获取或使用更完整的数据）
const regionData = {
  provinces: ['北京市', '上海市', '广东省', '江苏省', '浙江省', '四川省', '湖北省', '福建省'],
  cities: {
    '北京市': ['北京市'],
    '上海市': ['上海市'],
    '广东省': ['广州市', '深圳市', '佛山市', '东莞市', '珠海市'],
    '江苏省': ['南京市', '苏州市', '无锡市', '常州市', '南通市'],
    '浙江省': ['杭州市', '宁波市', '温州市', '嘉兴市', '湖州市'],
    '四川省': ['成都市', '绵阳市', '德阳市', '自贡市', '攀枝花市'],
    '湖北省': ['武汉市', '宜昌市', '襄阳市', '荆州市', '黄石市'],
    '福建省': ['福州市', '厦门市', '泉州市', '莆田市', '漳州市']
  },
  districts: {
    '北京市': {
      '北京市': ['东城区', '西城区', '朝阳区', '海淀区', '丰台区', '石景山区']
    },
    '上海市': {
      '上海市': ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区']
    },
    '广东省': {
      '广州市': ['越秀区', '荔湾区', '海珠区', '天河区', '白云区', '黄埔区'],
      '深圳市': ['福田区', '罗湖区', '南山区', '宝安区', '龙岗区', '盐田区'],
      '佛山市': ['禅城区', '南海区', '顺德区', '三水区', '高明区'],
      '东莞市': ['莞城街道', '东城街道', '南城街道', '万江街道', '石碣镇'],
      '珠海市': ['香洲区', '斗门区', '金湾区']
    },
    '江苏省': {
      '南京市': ['玄武区', '秦淮区', '建邺区', '鼓楼区', '浦口区', '栖霞区'],
      '苏州市': ['姑苏区', '虎丘区', '吴中区', '相城区', '吴江区'],
      '无锡市': ['梁溪区', '新吴区', '锡山区', '惠山区', '滨湖区'],
      '常州市': ['天宁区', '钟楼区', '新北区', '武进区', '金坛区'],
      '南通市': ['崇川区', '港闸区', '通州区', '如东县', '海门区']
    },
    '浙江省': {
      '杭州市': ['上城区', '下城区', '江干区', '拱墅区', '西湖区', '滨江区'],
      '宁波市': ['海曙区', '江北区', '北仑区', '镇海区', '鄞州区', '奉化区'],
      '温州市': ['鹿城区', '龙湾区', '瓯海区', '洞头区', '永嘉县'],
      '嘉兴市': ['南湖区', '秀洲区', '嘉善县', '海盐县', '海宁市'],
      '湖州市': ['吴兴区', '南浔区', '德清县', '长兴县', '安吉县']
    },
    '四川省': {
      '成都市': ['锦江区', '青羊区', '金牛区', '武侯区', '成华区', '龙泉驿区'],
      '绵阳市': ['涪城区', '游仙区', '安州区', '三台县', '盐亭县'],
      '德阳市': ['旌阳区', '罗江区', '中江县', '广汉市', '什邡市'],
      '自贡市': ['自流井区', '贡井区', '大安区', '沿滩区', '荣县'],
      '攀枝花市': ['东区', '西区', '仁和区', '米易县', '盐边县']
    },
    '湖北省': {
      '武汉市': ['江岸区', '江汉区', '硚口区', '汉阳区', '武昌区', '青山区'],
      '宜昌市': ['西陵区', '伍家岗区', '点军区', '猇亭区', '夷陵区'],
      '襄阳市': ['襄城区', '樊城区', '襄州区', '南漳县', '谷城县'],
      '荆州市': ['沙市区', '荆州区', '公安县', '监利县', '江陵县'],
      '黄石市': ['黄石港区', '西塞山区', '下陆区', '铁山区', '阳新县']
    },
    '福建省': {
      '福州市': ['鼓楼区', '台江区', '仓山区', '马尾区', '晋安区', '长乐区'],
      '厦门市': ['思明区', '海沧区', '湖里区', '集美区', '同安区', '翔安区'],
      '泉州市': ['鲤城区', '丰泽区', '洛江区', '泉港区', '惠安县'],
      '莆田市': ['城厢区', '涵江区', '荔城区', '秀屿区', '仙游县'],
      '漳州市': ['芗城区', '龙文区', '云霄县', '漳浦县', '诏安县']
    }
  }
};

Page({
  data: {
    address: {
      name: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      detail: '',
      isDefault: false
    },
    region: ['', '', ''],
    isEdit: false,
    addressId: '',
    
    // 自定义地区选择器相关数据
    showRegionSelector: false,
    provinces: regionData.provinces,
    cities: [],
    districts: [],
    provinceIndex: -1,
    cityIndex: -1,
    districtIndex: -1,
    currentTabIndex: 0, // 当前显示的tab：0-省, 1-市, 2-区
  },

  onLoad(options) {
    console.log('页面加载参数:', options);
    
    // 初始化空的region数组
    this.setData({
      region: ['', '', '']
    });
    
    if (options.id) {
      this.setData({ 
        isEdit: true,
        addressId: options.id
      });
      
      // 获取地址详情
      this.getAddressDetail(options.id);
    } else {
      // 新增模式，获取默认region
      try {
        // 尝试从缓存读取最近一次使用的地区
        const lastRegion = wx.getStorageSync('lastSelectedRegion');
        if (lastRegion && Array.isArray(lastRegion) && lastRegion.length === 3 && lastRegion[0]) {
          this.setData({ region: lastRegion });
          console.log('加载上次使用的地区:', lastRegion);
        }
      } catch (err) {
        console.error('读取缓存地区失败:', err);
      }
    }
    
    console.log('页面加载完成，region:', this.data.region);
  },

  // 切换到省份选择
  switchToProvince() {
    this.setData({
      currentTabIndex: 0
    });
    console.log('切换到省份选择');
  },

  // 切换到城市选择
  switchToCity() {
    if (this.data.provinceIndex >= 0) {
      this.setData({
        currentTabIndex: 1
      });
      console.log('切换到城市选择');
    }
  },

  // 切换到区县选择
  switchToDistrict() {
    if (this.data.cityIndex >= 0) {
      this.setData({
        currentTabIndex: 2
      });
      console.log('切换到区县选择');
    }
  },

  // 选择省份
  selectProvince(e) {
    const index = e.currentTarget.dataset.index;
    const province = this.data.provinces[index];
    const cities = regionData.cities[province] || [];
    
    this.setData({
      provinceIndex: index,
      cities: cities,
      cityIndex: -1,
      districts: [],
      districtIndex: -1,
      currentTabIndex: 1 // 自动切换到城市选择
    });
    
    console.log('选择省份:', province, '城市列表:', cities);
  },

  // 选择城市
  selectCity(e) {
    const index = e.currentTarget.dataset.index;
    const province = this.data.provinces[this.data.provinceIndex];
    const city = this.data.cities[index];
    const districts = regionData.districts[province][city] || [];
    
    this.setData({
      cityIndex: index,
      districts: districts,
      districtIndex: -1,
      currentTabIndex: 2 // 自动切换到区县选择
    });
    
    console.log('选择城市:', city, '区县列表:', districts);
  },

  // 选择区县
  selectDistrict(e) {
    const index = e.currentTarget.dataset.index;
    
    this.setData({
      districtIndex: index
    });
    
    const district = this.data.districts[index];
    console.log('选择区县:', district);
  },

  // 获取地址详情
  getAddressDetail(id) {
    wx.showLoading({ title: '加载中' });
    
    if (!id) {
      console.error('获取地址详情失败: 缺少ID');
      wx.hideLoading();
      return;
    }
    
    // 尝试使用云函数获取地址信息
    wx.cloud.callFunction({
      name: 'baseFunction',
      data: {
        action: 'getAddressDetail',
        data: {
          addressId: id
        }
      }
    })
    .then(res => {
      console.log('云函数获取地址详情:', res);
      if (res.result && res.result.code === 0 && res.result.data) {
        const address = res.result.data;
        console.log('获取到地址详情:', address);
        
        // 确保所有字段都存在
        if (address.province && address.city && address.district) {
          this.setData({
            address,
            region: [address.province, address.city, address.district]
          });
          console.log('设置region后:', this.data.region);
        } else {
          console.error('地址数据缺少province/city/district字段:', address);
          wx.showToast({
            title: '地址数据不完整',
            icon: 'none'
          });
        }
      } else {
        console.error('获取地址详情失败:', res.result ? res.result.msg : '无响应数据');
        wx.showToast({
          title: '获取地址信息失败',
          icon: 'none'
        });
      }
      wx.hideLoading();
    })
    .catch(err => {
      console.error('获取地址详情失败:', err);
      this.handleError(err);
      wx.hideLoading();
    });
  },

  // 提交表单
  submitForm(e) {
    console.log('========== 提交表单按钮被点击 ==========');
    console.log('提交表单:', e.detail.value);
    console.log('当前地区:', this.data.region);
    
    const formData = e.detail.value;
    const userInfo = wx.getStorageSync('userInfo');
    
    console.log('当前用户信息:', userInfo);
    
    if (!userInfo || !userInfo.dbUserId) {
      console.error('用户未登录或缺少dbUserId');
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 表单验证
    if (!this.validateForm(formData)) {
      console.log('表单验证失败');
      return;
    }

    // 检查地区是否已选择
    if (!this.data.region[0] || !this.data.region[1] || !this.data.region[2]) {
      console.error('地区未完全选择', this.data.region);
      wx.showToast({
        title: '请完整选择所在地区',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    wx.showLoading({ 
      title: '保存中',
      mask: true  // 添加蒙层防止重复点击
    });

    // 构建地址数据
    const addressData = {
      userId: userInfo.dbUserId,  // 添加用户ID
      name: formData.name,
      phone: formData.phone,
      province: this.data.region[0],
      city: this.data.region[1],
      district: this.data.region[2],
      detail: formData.detail,
      isDefault: formData.isDefault,
      createTime: db.serverDate()
    };

    console.log('准备发送的地址数据:', addressData);

    // 如果是编辑模式
    if (this.data.isEdit && this.data.addressId) {
      // 使用云函数更新地址
      wx.cloud.callFunction({
        name: 'baseFunction',
        data: {
          action: 'updateAddress',
          data: {
            addressId: this.data.addressId,
            name: formData.name,
            phone: formData.phone,
            province: this.data.region[0],
            city: this.data.region[1],
            district: this.data.region[2],
            detail: formData.detail,
            isDefault: formData.isDefault
          }
        }
      })
      .then(res => {
        console.log('更新地址结果:', res);
        if (res.result && res.result.code === 0) {
          this.handleSuccess();
        } else {
          this.handleError(new Error(res.result ? res.result.msg : '更新失败，无响应数据'));
        }
      })
      .catch(err => {
        console.error('调用云函数失败:', err);
        this.handleError(err);
      });
    } else {
      // 使用云函数创建新地址
      wx.cloud.callFunction({
        name: 'baseFunction',
        data: {
          action: 'createAddress',
          data: {
            userId: userInfo.dbUserId,
            name: formData.name,
            phone: formData.phone,
            province: this.data.region[0],
            city: this.data.region[1],
            district: this.data.region[2],
            detail: formData.detail,
            isDefault: formData.isDefault
          }
        }
      })
      .then(res => {
        console.log('创建地址结果:', res);
        if (res.result && res.result.code === 0) {
          this.handleSuccess();
        } else {
          this.handleError(new Error(res.result ? res.result.msg : '创建失败，无响应数据'));
        }
      })
      .catch(err => {
        console.error('调用云函数失败:', err);
        this.handleError(err);
      });
    }
  },

  validateForm(formData) {
    console.log('验证表单:', formData);
    console.log('当前地区:', this.data.region);
    
    if (!formData.name) {
      this.showToast('请输入收货人');
      return false;
    }
    if (!formData.phone) {
      this.showToast('请输入手机号码');
      return false;
    }
    if (!this.data.region[0]) {
      this.showToast('请选择所在地区');
      return false;
    }
    if (!formData.detail) {
      this.showToast('请输入详细地址');
      return false;
    }
    return true;
  },

  showToast(title) {
    wx.showToast({
      title,
      icon: 'none'
    });
  },

  // 处理成功
  handleSuccess() {
    wx.hideLoading();
    wx.showToast({
      title: '保存成功',
      icon: 'success'
    });
    // 返回上一页并刷新列表
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];
    if (prevPage && prevPage.route === 'pages/address/address') {
      prevPage.getAddressList();
    }
    wx.navigateBack();
  },

  // 处理错误
  handleError(err) {
    const errorInfo = {
      错误: err.message || '未知错误',
      错误类型: err.name || '未知类型',
      错误堆栈: err.stack || '无堆栈信息',
      时间: new Date().toISOString()
    };
    
    console.error('[数据库] 保存地址失败:', errorInfo);
    
    // 确保关闭loading
    wx.hideLoading();
    
    // 显示更具体的错误信息
    let errorMsg = '保存失败';
    
    // 尝试提取更具体的错误信息
    if (err.message && err.message.includes('timeout')) {
      errorMsg = '网络超时，请重试';
    } else if (err.message && err.message.includes('request:fail')) {
      errorMsg = '网络连接失败，请检查网络';
    }
    
    wx.showToast({
      title: errorMsg,
      icon: 'error',
      duration: 2000
    });
    
    // 如果是云函数调用失败，可能是云环境问题
    if (err.errCode === -404) {
      setTimeout(() => {
        wx.showModal({
          title: '提示',
          content: '系统服务异常，请稍后再试',
          showCancel: false
        });
      }, 1500);
    }
  },

  // 地区选择器相关方法
  showRegionSelector() {
    console.log('打开地区选择器');
    
    // 如果已有选择的地区，则设置相应的索引
    const { region } = this.data;
    if (region[0]) {
      const provinceIndex = regionData.provinces.findIndex(p => p === region[0]);
      if (provinceIndex !== -1) {
        const cities = regionData.cities[region[0]] || [];
        const cityIndex = cities.findIndex(c => c === region[1]);
        
        let districts = [];
        let districtIndex = -1;
        
        if (cityIndex !== -1) {
          districts = regionData.districts[region[0]][region[1]] || [];
          districtIndex = districts.findIndex(d => d === region[2]);
        }
        
        this.setData({
          provinceIndex,
          cities,
          cityIndex,
          districts,
          districtIndex,
          currentTabIndex: (districtIndex !== -1) ? 2 : ((cityIndex !== -1) ? 1 : 0)
        });
      }
    } else {
      // 没有已选择的地区，初始化省份列表
      this.setData({
        provinceIndex: -1,
        cities: [],
        cityIndex: -1,
        districts: [],
        districtIndex: -1,
        currentTabIndex: 0
      });
    }
    
    this.setData({
      showRegionSelector: true
    });

    console.log('当前省市区选择器状态:', {
      provinces: this.data.provinces,
      cities: this.data.cities,
      districts: this.data.districts,
      provinceIndex: this.data.provinceIndex,
      cityIndex: this.data.cityIndex,
      districtIndex: this.data.districtIndex
    });
  },
  
  hideRegionSelector() {
    this.setData({
      showRegionSelector: false
    });
  },
  
  stopPropagation() {
    // 阻止冒泡，防止点击弹窗内容时关闭弹窗
    return;
  },

  confirmRegion() {
    const { provinceIndex, cityIndex, districtIndex, provinces, cities, districts } = this.data;
    
    if (provinceIndex !== -1 && cityIndex !== -1 && districtIndex !== -1) {
      const province = provinces[provinceIndex];
      const city = cities[cityIndex];
      const district = districts[districtIndex];
      
      console.log('确认选择地区:', [province, city, district]);
      
      this.setData({
        region: [province, city, district],
        showRegionSelector: false
      });
      
      // 保存到缓存
      try {
        wx.setStorageSync('lastSelectedRegion', [province, city, district]);
      } catch (err) {
        console.error('保存地区到缓存失败:', err);
      }
    } else {
      wx.showToast({
        title: '请完整选择所在地区',
        icon: 'none'
      });
    }
  },
}); 