const db = wx.cloud.database()

Page({
  data: {
    order: null,
    statusText: {
      0: '待付款',
      1: '待发货',
      2: '已发货',
      3: '已完成',
      4: '已取消',
      5: '退款处理中',
      6: '退款已完成'
    },
    loading: true
  },

  onLoad(options) {
    if (options.id) {
      this.getOrderDetail(options.id);
    }
  },

  async getOrderDetail(orderId) {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.dbUserId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '加载中' });

    try {
      const res = await db.collection('orders')
        .doc(orderId)
        .get();

      if (!res.data) {
        throw new Error('订单不存在');
      }

      if (res.data.userId !== userInfo.dbUserId) {
        throw new Error('无权查看此订单');
      }

      console.log('[订单] 获取订单详情成功:', {
        订单ID: orderId,
        订单状态: res.data.status
      });

      const order = res.data;
      order.createTime = this.formatDate(order.createTime);
      if(order.payTime) {
        order.payTime = this.formatDate(order.payTime);
      }
      if(order.refundInfo) {
        order.refundInfo.applyTime = this.formatDate(order.refundInfo.applyTime);
        if(order.refundInfo.handleTime) {
          order.refundInfo.handleTime = this.formatDate(order.refundInfo.handleTime);
        }
      }
      this.setData({
        order,
        loading: false
      });

    } catch (err) {
      console.error('[订单] 获取订单详情失败:', {
        错误: err.message,
        错误类型: err.name,
        错误堆栈: err.stack,
        订单ID: orderId,
        时间: new Date().toISOString()
      });

      wx.showToast({
        title: '获取订单失败',
        icon: 'error'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } finally {
      wx.hideLoading();
    }
  },

  formatDate(date) {
    if (!date) return '';
    date = new Date(date);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  },

  async payOrder() {
    const { order } = this.data;
    if (!order || order.status !== 0) return;

    wx.showLoading({ title: '正在支付...' });
    
    try {
      await paymentService.createPayment({
        orderId: order._id,
        amount: order.totalAmount,
        description: `UW商城订单-${order.orderNo}`
      });
      
      await db.collection('orders').doc(order._id).update({
        data: {
          status: 1,
          statusHistory: db.command.push({
            status: 1,
            time: db.serverDate(),
            desc: '支付成功'
          }),
          updateTime: db.serverDate()
        }
      });
      
      wx.hideLoading();
      wx.showToast({
        title: '支付成功',
        icon: 'success'
      });
      
      this.getOrderDetail(order._id);
      
    } catch (err) {
      console.error('[支付] 支付失败:', err);
      wx.hideLoading();
      
      if (err.errMsg === 'requestPayment:fail cancel') {
        wx.showToast({
          title: '支付已取消',
          icon: 'none'
        });
      } else {
        wx.showToast({
          title: '支付失败，请重试',
          icon: 'error'
        });
      }
    }
  }
}); 