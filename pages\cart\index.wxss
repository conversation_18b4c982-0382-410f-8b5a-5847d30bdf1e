/* pages/cart/index.wxss */
.container {
  min-height: 100vh;
  background: #f8f8f8;
  padding-bottom: 120rpx;
}

.cart-list {
  padding: 20rpx;
}

.cart-item {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.checkbox {
  width: 44rpx;
  height: 44rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-inner {
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  transition: all 0.2s;
}

.check-inner.selected {
  background: #987;
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  width: 0;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 10rpx;
}

.product-spec {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.price-quantity {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-size: 32rpx;
  color: #987;
  font-weight: bold;
  margin-left: 20rpx;
}

.quantity-control {
  display: flex;
  align-items: center;
  margin-right: auto;
  transform: scale(0.9);
  transform-origin: left center;
}

.minus, .plus {
  width: 40rpx;
  height: 40rpx;
  border: 1rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.number {
  width: 50rpx;
  text-align: center;
  font-size: 24rpx;
}

.delete {
  padding: 10rpx 20rpx;
  color: #999;
  font-size: 24rpx;
}

.empty {
  text-align: center;
  padding: 100rpx 0;
}

.go-shop {
  margin-top: 30rpx;
  width: 200rpx;
  background: #987;
  color: #fff;
  font-size: 28rpx;
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}

.select-all {
  display: flex;
  align-items: center;
}

.total {
  flex: 1;
  text-align: right;
  margin-right: 20rpx;
  font-size: 28rpx;
}

.checkout-btn {
  width: 200rpx;
  height: 80rpx;
  background: #987;
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100rpx;
  margin-top: 200rpx;
}

.loading image {
  width: 60rpx;
  height: 60rpx;
}