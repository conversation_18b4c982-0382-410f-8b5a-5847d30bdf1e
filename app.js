const ENV = require('./config/env');

App({
  globalData: {
    cloudEnv: ENV.CLOUD_ENV,
    defaultImages: ENV.DEFAULT_IMAGES,
    userInfo: null,
    cartCount: 0
  },

  onLaunch() {
    // 初始化云开发
    if (wx.cloud) {
      wx.cloud.init({
        env: ENV.CLOUD_ENV,
        traceUser: true
      });
      console.log('[云开发] 初始化成功，环境ID:', ENV.CLOUD_ENV);
    } else {
      console.error('[云开发] 初始化失败，请检查云开发配置');
    }
  },

  onShow() {
    // 应用显示时的逻辑
  },

  onHide() {
    // 应用隐藏时的逻辑
  }
})





