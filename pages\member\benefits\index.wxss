.container {
  min-height: 100vh;
  background: #f8f8f8;
  padding: 20rpx;
}

.header {
  padding: 30rpx 0;
  text-align: center;
}

.title {
  font-size: 32rpx;
  color: #333;
  font-family: "Source Han Sans CN", "思源黑体", sans-serif;
}

.coupon-list {
  padding: 20rpx 0;
}

.coupon-item {
  display: flex;
  background: linear-gradient(to right, #987, #b9a89f);
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  color: #fff;
  position: relative;
  overflow: hidden;
}

.coupon-item.used {
  background: #ccc;
  opacity: 0.8;
}

.left {
  width: 200rpx;
  border-right: 2rpx dashed rgba(255,255,255,0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.amount {
  display: flex;
  align-items: baseline;
}

.symbol {
  font-size: 32rpx;
}

.number {
  font-size: 60rpx;
  font-weight: bold;
  margin-left: 4rpx;
}

.condition {
  font-size: 24rpx;
  margin-top: 10rpx;
}

.right {
  flex: 1;
  padding-left: 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.date {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 20rpx;
}

.get-btn {
  width: 160rpx;
  height: 60rpx;
  line-height: 60rpx;
  background: rgba(255, 255, 255, 0.9);
  color: #987;
  font-size: 26rpx;
  border-radius: 30rpx;
  text-align: center;
  padding: 0;
}

.get-btn.disabled {
  background: rgba(255, 255, 255, 0.5);
  color: rgba(152, 136, 119, 0.5);
} 