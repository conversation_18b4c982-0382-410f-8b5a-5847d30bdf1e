const ENV = require('../../config/env');

Page({
  data: {
    productList: [],
    loading: true,
    categories: ['全部', '热销', '新品', '推荐'],
    currentCategory: 0
  },

  onLoad() {
    console.log('[商城页面] 页面加载');
    this.getProductList();
  },

  onShow() {
    console.log('[商城页面] 页面显示');
  },

  // 获取商品列表
  async getProductList() {
    console.log('[商城页面] 开始获取商品列表');
    
    this.setData({ loading: true });
    
    try {
      // 模拟商品数据，避免云数据库依赖
      const mockProducts = [
        {
          _id: '1',
          name: '测试商品1',
          shortName: '商品1',
          price: 99.00,
          originalPrice: 129.00,
          mainImage: '/images/logo.png'
        },
        {
          _id: '2', 
          name: '测试商品2',
          shortName: '商品2',
          price: 199.00,
          mainImage: '/images/logo.png'
        }
      ];
      
      this.setData({
        productList: mockProducts,
        loading: false
      });
      
      console.log('[商城页面] 商品列表加载成功:', mockProducts);
      
    } catch (err) {
      console.error('[商城页面] 获取商品列表失败:', err);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 跳转到商品详情
  goToDetail(e) {
    const productId = e.currentTarget.dataset.id;
    console.log('[商城页面] 跳转到商品详情:', productId);
    
    wx.navigateTo({
      url: `/pages/product/detail?id=${productId}`
    });
  },

  // 跳转到购物车
  goToCart() {
    console.log('[商城页面] 跳转到购物车');
    wx.navigateTo({
      url: '/pages/cart/cart'
    });
  },

  // 图片加载错误处理
  onImageError(e) {
    const index = e.currentTarget.dataset.index;
    console.log('[商城页面] 图片加载失败，索引:', index);
    
    // 使用默认图片替换
    const productList = this.data.productList;
    if (productList[index]) {
      productList[index].mainImage = ENV.DEFAULT_IMAGES.PRODUCT;
      this.setData({ productList });
    }
  },

  // 分类切换
  onCategoryTap(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({ currentCategory: index });
    this.getProductList();
  }
});

