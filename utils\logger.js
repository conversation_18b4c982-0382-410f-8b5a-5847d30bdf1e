const logger = {
  info: (context, message, data = {}) => {
    console.log(`[${context}] ${message}`, {
      ...data,
      time: new Date().toISOString()
    });
  },

  error: (context, message, error = {}) => {
    // 安全处理不同格式的错误对象
    const errorObj = error || {};
    const errorMessage = errorObj.message || errorObj.errMsg || 
                         (typeof errorObj === 'string' ? errorObj : '未知错误');
    const errorStack = errorObj.stack || '';
    
    // 构建更完整的错误信息对象
    const errorData = {
      message: errorMessage,
      stack: errorStack,
      code: errorObj.code,
      errMsg: errorObj.errMsg, // 微信API错误通常在errMsg中
      originalError: error,    // 保留原始错误对象以便调试
      time: new Date().toISOString()
    };
    
    console.error(`[${context}] ${message}`, errorData);
  },

  warn: (context, message, data = {}) => {
    console.warn(`[${context}] ${message}`, {
      ...data,
      time: new Date().toISOString()
    });
  }
};

module.exports = logger; 