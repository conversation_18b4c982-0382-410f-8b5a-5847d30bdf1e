.container {
  min-height: 100vh;
  background: #f8f8f8;
}

/* 导航栏样式 */
.nav-bar {
  display: flex;
  background: #fff;
  padding: 20rpx 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
  border-bottom: 1rpx solid #eee;
}

.nav-item {
  flex: 1;
  text-align: center;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.nav-text {
  font-size: 28rpx;
  color: #333;
  padding: 10rpx 0;
}

.nav-item.active {
}

.nav-item.active .nav-text {
  color: #987;
  font-weight: 500;
}

.nav-line {
  width: 40rpx;
  height: 4rpx;
  background: #987;
  border-radius: 2rpx;
  margin-top: 6rpx;
}

/* 订单列表样式 */
.order-list {
  margin-top: 100rpx;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  width: 100%;
}

.order-item {
  position: relative;
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.order-item::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: #000;
  opacity: 0;
  transition: opacity 0.2s;
}

.order-item:active::after {
  opacity: 0.1;
}

.order-header {
  display: flex;
  justify-content: space-between;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.order-no {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  font-size: 24rpx;
  color: #987;
}

.order-content {
  display: flex;
  flex-direction: column;
  padding: 20rpx 0;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

.product-item {
  display: flex;
  margin-bottom: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.product-item:last-child {
  margin-bottom: 0;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  min-width: 0;
  width: calc(100% - 140rpx);  /* 减去图片宽度和margin */
}

.product-name {
  font-size: 28rpx;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-spec {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.price-quantity {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-size: 28rpx;
  color: #987;
}

.quantity {
  font-size: 24rpx;
  color: #999;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
  flex-wrap: nowrap;
  gap: 10rpx;
  width: 100%;
  box-sizing: border-box;
  min-width: 0;
}

.total {
  font-size: 28rpx;
  color: #333;
  flex-shrink: 1;
  margin-right: 16rpx;
  white-space: nowrap;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.btn {
  font-size: 24rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  background: #987;
  color: #fff;
  border: none;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.empty {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

.action-btn {
  min-width: 140rpx;
  max-width: 160rpx;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  border-radius: 30rpx;
  font-size: 24rpx;
  margin-left: 16rpx;
  padding: 0 20rpx;
  position: relative;
  z-index: 10;
  white-space: nowrap;
  overflow: hidden;
}

.pay-btn {
  background: #987;
  color: #fff;
  border: none;
}

.cancel-btn {
  background: #fff;
  color: #666;
  border: 1rpx solid #ddd;
}

.confirm-btn {
  background: #987;
  color: #fff;
  border: none;
}

.refund-btn {
  background: #fff;
  color: #666;
  border: 1rpx solid #ddd;
}

.order-actions {
  display: flex;
  gap: 12rpx;
  position: relative;
  z-index: 5;
  flex-wrap: nowrap;
  justify-content: flex-end;
  align-items: center;
  min-width: 0;
}

.countdown-bar {
  padding: 16rpx 20rpx;
  font-size: 24rpx;
  color: #ff4d4f;
  display: flex;
  align-items: center;
  background: #fff7f7;
  border-bottom: 1rpx solid #eee;
}

.countdown-text {
  color: #666;
  font-size: 22rpx;
}

.countdown-time {
  color: #ff4d4f;
  margin: 0 8rpx;
  font-weight: 500;
  font-family: monospace;
} 