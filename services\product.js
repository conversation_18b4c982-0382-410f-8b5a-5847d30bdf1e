import request from '../utils/request';

const API = {
  PRODUCTS: '/api/products',
  PRODUCT_DETAIL: '/api/products/detail',
  PRODUCT_CATEGORIES: '/api/products/categories'
};

const productService = {
  // 获取商品列表
  getProducts: async function(params = {}) {
    try {
      return await request.get(API.PRODUCTS, {
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        category: params.category,
        keyword: params.keyword
      });
    } catch (error) {
      console.error('获取商品列表失败:', error);
      throw error;
    }
  },

  // 获取商品详情
  getProductDetail: async function(id) {
    try {
      return await request.get(`${API.PRODUCT_DETAIL}/${id}`);
    } catch (error) {
      console.error('获取商品详情失败:', error);
      throw error;
    }
  },

  // 获取商品分类
  getCategories: async function() {
    try {
      return await request.get(API.PRODUCT_CATEGORIES);
    } catch (error) {
      console.error('获取商品分类失败:', error);
      throw error;
    }
  }
};

export default productService; 