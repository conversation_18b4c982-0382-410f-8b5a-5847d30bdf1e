// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    console.log('[支付回调转发] 收到回调请求:', {
      请求方法: event.httpMethod,
      请求路径: event.path,
      请求头: event.headers
    });
    
    // 转发请求到wxpayFunctions的processNotify处理
    const result = await cloud.callFunction({
      name: 'wxpayFunctions',
      data: {
        action: 'processNotify',
        ...event
      }
    });
    
    console.log('[支付回调转发] 处理结果:', result);
    
    // 返回结果给微信支付
    return result.result;
  } catch (err) {
    console.error('[支付回调转发] 处理失败:', err);
    
    // 返回错误响应
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        code: 'FAIL',
        message: '处理失败'
      })
    };
  }
} 