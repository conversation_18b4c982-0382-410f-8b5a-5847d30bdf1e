{"meta": {"caseId": "f1a63351-14d5-422e-b795-1b41cfec4062", "caseName": "minitest-1", "remarks": "", "version": "0.0.1", "startTime": 1744880067832, "endTime": 1744880109141, "appid": "wxf57755081128c53d", "casePath": "", "caseType": "normal", "commonParams": [{"key": "1744880109159", "name": "", "value": ""}]}, "env": {"wxapplibVersion": "3.7.8", "devtoolsVersion": "1.06.2412050", "openid": "o6zAJs0ZX9oSdeKIgKnliB8jZqPc", "entry": {"path": "pages/index/index", "query": "", "scene": 1001}, "mock": ["none"], "maxWaitFor": 10000, "ignoreSelectors": [], "mockAPIs": [], "cleanCache": ["none"], "groupTouch": false, "replaySetting": {"screenshot": "none"}, "simulatorType": "wechat", "device": "iPhone 15 Pro Max", "recordAPIs": []}, "commands": [{"layer": "tool", "commandType": "operate", "command": "startRecord", "waitfor": 0, "timeStamp": 1744880067832, "desc": ""}, {"timeStamp": 6236, "waitfor": 6236, "desc": "", "commandType": "operate", "layer": "native", "command": "switchTab", "text": "品牌商城", "data": {"url": "pages/shop/shop"}}, {"tagName": "view", "command": "touchstart", "target": "view.product-grid", "value": null, "path": "pages/shop/shop", "text": "UW防脱精华液\n¥198\n¥298", "eventData": {"detail": {}, "changedTouches": [{"clientX": 234.03750610351562, "clientY": 511.5500183105469, "force": 1, "pageX": 234.03750610351562, "pageY": 511.5500183105469, "screenX": 638.4000244140625, "screenY": 705.6000366210938}], "touches": [{"clientX": 234.03750610351562, "clientY": 511.5500183105469, "force": 1, "pageX": 234.03750610351562, "pageY": 511.5500183105469, "screenX": 638.4000244140625, "screenY": 705.6000366210938}]}, "targetCandidates": ["view.product-grid", ".product-grid", "/view/view[1]"], "timeStamp": 8417, "waitfor": 2181, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "view", "command": "touchend", "target": "view.product-grid", "value": null, "path": "pages/shop/shop", "text": "UW防脱精华液\n¥198\n¥298", "eventData": {"detail": {}, "changedTouches": [{"clientX": 234.03750610351562, "clientY": 511.5500183105469, "pageX": 234.03750610351562, "pageY": 511.5500183105469, "screenX": 638.4000244140625, "screenY": 705.6000366210938}], "touches": []}, "targetCandidates": ["view.product-grid", ".product-grid", "/view/view[1]"], "timeStamp": 8516, "waitfor": 99, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "view", "command": "tap", "target": "view.product-grid", "value": null, "path": "pages/shop/shop", "text": "UW防脱精华液\n¥198\n¥298", "eventData": {"detail": {"x": 234.03750610351562, "y": 511.5500183105469}, "changedTouches": [{"clientX": 234.03750610351562, "clientY": 511.5500183105469, "force": 1, "pageX": 234.03750610351562, "pageY": 511.5500183105469, "screenX": 638.4000244140625, "screenY": 705.6000366210938}], "touches": [{"clientX": 234.03750610351562, "clientY": 511.5500183105469, "force": 1, "pageX": 234.03750610351562, "pageY": 511.5500183105469, "screenX": 638.4000244140625, "screenY": 705.6000366210938}]}, "targetCandidates": ["view.product-grid", ".product-grid", "/view/view[1]"], "timeStamp": 8517, "waitfor": 1, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "image", "command": "touchstart", "target": "image.product-image", "value": null, "path": "pages/shop/shop", "text": "", "eventData": {"detail": {}, "changedTouches": [{"clientX": 138.83750915527344, "clientY": 173.95001220703125, "force": 1, "pageX": 138.83750915527344, "pageY": 173.95001220703125, "screenX": 543.2000122070312, "screenY": 368}], "touches": [{"clientX": 138.83750915527344, "clientY": 173.95001220703125, "force": 1, "pageX": 138.83750915527344, "pageY": 173.95001220703125, "screenX": 543.2000122070312, "screenY": 368}]}, "targetCandidates": ["image.product-image", ".product-image", "/view/view[1]/view/image"], "timeStamp": 9465, "waitfor": 948, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "image", "command": "touchend", "target": "image.product-image", "value": null, "path": "pages/shop/shop", "text": "", "eventData": {"detail": {}, "changedTouches": [{"clientX": 138.83750915527344, "clientY": 173.95001220703125, "pageX": 138.83750915527344, "pageY": 173.95001220703125, "screenX": 543.2000122070312, "screenY": 368}], "touches": []}, "targetCandidates": ["image.product-image", ".product-image", "/view/view[1]/view/image"], "timeStamp": 9718, "waitfor": 253, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "image", "command": "tap", "target": "image.product-image", "value": null, "path": "pages/shop/shop", "text": "", "eventData": {"detail": {"x": 138.83750915527344, "y": 173.95001220703125}, "changedTouches": [{"clientX": 138.83750915527344, "clientY": 173.95001220703125, "force": 1, "pageX": 138.83750915527344, "pageY": 173.95001220703125, "screenX": 543.2000122070312, "screenY": 368}], "touches": [{"clientX": 138.83750915527344, "clientY": 173.95001220703125, "force": 1, "pageX": 138.83750915527344, "pageY": 173.95001220703125, "screenX": 543.2000122070312, "screenY": 368}]}, "targetCandidates": ["image.product-image", ".product-image", "/view/view[1]/view/image"], "timeStamp": 9719, "waitfor": 1, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "", "command": "scroll", "target": "/", "path": "pages/product/detail", "eventData": {"scrollDetail": {"scrollTop": 100, "scrollLeft": 0}}, "targetCandidates": [], "timeStamp": 12652, "waitfor": 2933, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "view", "command": "touchstart", "target": "/scroll-view/view[2]/view/view[1]", "value": null, "path": "pages/product/detail", "text": "42ml", "eventData": {"detail": {}, "changedTouches": [{"clientX": 98.03750610351562, "clientY": 503.54998779296875, "force": 1, "pageX": 98.03750610351562, "pageY": 603.5499877929688, "screenX": 502.3999938964844, "screenY": 697.6000366210938}], "touches": [{"clientX": 98.03750610351562, "clientY": 503.54998779296875, "force": 1, "pageX": 98.03750610351562, "pageY": 603.5499877929688, "screenX": 502.3999938964844, "screenY": 697.6000366210938}]}, "targetCandidates": ["/scroll-view/view[2]/view/view[1]"], "timeStamp": 13526, "waitfor": 874, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "view", "command": "touchend", "target": "/scroll-view/view[2]/view/view[1]", "value": null, "path": "pages/product/detail", "text": "42ml", "eventData": {"detail": {}, "changedTouches": [{"clientX": 98.03750610351562, "clientY": 503.54998779296875, "pageX": 98.03750610351562, "pageY": 603.5499877929688, "screenX": 502.3999938964844, "screenY": 697.6000366210938}], "touches": []}, "targetCandidates": ["/scroll-view/view[2]/view/view[1]"], "timeStamp": 13565, "waitfor": 39, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "view", "command": "tap", "target": "/scroll-view/view[2]/view/view[1]", "value": null, "path": "pages/product/detail", "text": "42ml", "eventData": {"detail": {"x": 98.03750610351562, "y": 603.5499877929688}, "changedTouches": [{"clientX": 98.03750610351562, "clientY": 503.54998779296875, "force": 1, "pageX": 98.03750610351562, "pageY": 603.5499877929688, "screenX": 502.3999938964844, "screenY": 697.6000366210938}], "touches": [{"clientX": 98.03750610351562, "clientY": 503.54998779296875, "force": 1, "pageX": 98.03750610351562, "pageY": 603.5499877929688, "screenX": 502.3999938964844, "screenY": 697.6000366210938}]}, "targetCandidates": ["/scroll-view/view[2]/view/view[1]"], "timeStamp": 13565, "waitfor": 0, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "", "command": "scroll", "target": "/", "path": "pages/product/detail", "eventData": {"scrollDetail": {"scrollTop": 200, "scrollLeft": 0}}, "targetCandidates": [], "timeStamp": 14478, "waitfor": 913, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "", "command": "scroll", "target": "/", "path": "pages/product/detail", "eventData": {"scrollDetail": {"scrollTop": 400, "scrollLeft": 0}}, "targetCandidates": [], "timeStamp": 15291, "waitfor": 813, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "", "command": "scroll", "target": "/", "path": "pages/product/detail", "eventData": {"scrollDetail": {"scrollTop": 500, "scrollLeft": 0}}, "targetCandidates": [], "timeStamp": 15691, "waitfor": 400, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "button", "command": "touchstart", "target": "button.buy-now", "value": null, "path": "pages/product/detail", "text": "立即购买", "eventData": {"detail": {}, "changedTouches": [{"clientX": 357.2375183105469, "clientY": 805.1500244140625, "force": 1, "pageX": 357.2375183105469, "pageY": 1305.1500244140625, "screenX": 761.6000366210938, "screenY": 999.2000122070312}], "touches": [{"clientX": 357.2375183105469, "clientY": 805.1500244140625, "force": 1, "pageX": 357.2375183105469, "pageY": 1305.1500244140625, "screenX": 761.6000366210938, "screenY": 999.2000122070312}]}, "targetCandidates": ["button.buy-now", ".buy-now", "/scroll-view/view[5]/button[3]"], "timeStamp": 16517, "waitfor": 826, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "button", "command": "touchend", "target": "button.buy-now", "value": null, "path": "pages/product/detail", "text": "立即购买", "eventData": {"detail": {}, "changedTouches": [{"clientX": 357.2375183105469, "clientY": 805.1500244140625, "pageX": 357.2375183105469, "pageY": 1305.1500244140625, "screenX": 761.6000366210938, "screenY": 999.2000122070312}], "touches": []}, "targetCandidates": ["button.buy-now", ".buy-now", "/scroll-view/view[5]/button[3]"], "timeStamp": 16605, "waitfor": 88, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "button", "command": "tap", "target": "button.buy-now", "value": null, "path": "pages/product/detail", "text": "立即购买", "eventData": {"detail": {"x": 357.2375183105469, "y": 1305.1500244140625}, "changedTouches": [{"clientX": 357.2375183105469, "clientY": 805.1500244140625, "force": 1, "pageX": 357.2375183105469, "pageY": 1305.1500244140625, "screenX": 761.6000366210938, "screenY": 999.2000122070312}], "touches": [{"clientX": 357.2375183105469, "clientY": 805.1500244140625, "force": 1, "pageX": 357.2375183105469, "pageY": 1305.1500244140625, "screenX": 761.6000366210938, "screenY": 999.2000122070312}]}, "targetCandidates": ["button.buy-now", ".buy-now", "/scroll-view/view[5]/button[3]"], "timeStamp": 16605, "waitfor": 0, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "text", "command": "touchstart", "target": "/view[1]/view[1]/view[1]/text", "value": null, "childName": "main", "path": "pages/order/confirm", "text": "添加收货地址", "eventData": {"detail": {}, "changedTouches": [{"clientX": 142.03749084472656, "clientY": 42.75, "force": 1, "pageX": 142.03749084472656, "pageY": 42.75, "screenX": 546.4000244140625, "screenY": 236.8000030517578}], "touches": [{"clientX": 142.03749084472656, "clientY": 42.75, "force": 1, "pageX": 142.03749084472656, "pageY": 42.75, "screenX": 546.4000244140625, "screenY": 236.8000030517578}]}, "targetCandidates": ["/view[1]/view[1]/view[1]/text"], "timeStamp": 19521, "waitfor": 2916, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "text", "command": "touchend", "target": "/view[1]/view[1]/view[1]/text", "value": null, "childName": "main", "path": "pages/order/confirm", "text": "添加收货地址", "eventData": {"detail": {}, "changedTouches": [{"clientX": 142.03749084472656, "clientY": 42.75, "pageX": 142.03749084472656, "pageY": 42.75, "screenX": 546.4000244140625, "screenY": 236.8000030517578}], "touches": []}, "targetCandidates": ["/view[1]/view[1]/view[1]/text"], "timeStamp": 19615, "waitfor": 94, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "text", "command": "tap", "target": "/view[1]/view[1]/view[1]/text", "value": null, "childName": "main", "path": "pages/order/confirm", "text": "添加收货地址", "eventData": {"detail": {"x": 142.03749084472656, "y": 42.75}, "changedTouches": [{"clientX": 142.03749084472656, "clientY": 42.75, "force": 1, "pageX": 142.03749084472656, "pageY": 42.75, "screenX": 546.4000244140625, "screenY": 236.8000030517578}], "touches": [{"clientX": 142.03749084472656, "clientY": 42.75, "force": 1, "pageX": 142.03749084472656, "pageY": 42.75, "screenX": 546.4000244140625, "screenY": 236.8000030517578}]}, "targetCandidates": ["/view[1]/view[1]/view[1]/text"], "timeStamp": 19616, "waitfor": 1, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "view", "command": "touchstart", "target": "view.address", "value": null, "path": "pages/address/address", "text": "北京市北京市西城区12", "eventData": {"detail": {}, "changedTouches": [{"clientX": 176.4375, "clientY": 58.75, "pageX": 176.4375, "pageY": 58.75, "screenX": 580.7999877929688, "screenY": 364}], "touches": [{"clientX": 176.4375, "clientY": 58.75, "pageX": 176.4375, "pageY": 58.75, "screenX": 580.7999877929688, "screenY": 364}]}, "targetCandidates": ["view.address", ".address", "/view/view[1]/view/view[1]/view[2]"], "timeStamp": 23033, "waitfor": 3417, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "view", "command": "touchend", "target": "view.address", "value": null, "path": "pages/address/address", "text": "北京市北京市西城区12", "eventData": {"detail": {}, "changedTouches": [{"clientX": 176.4375, "clientY": 58.75, "pageX": 176.4375, "pageY": 58.75, "screenX": 580.7999877929688, "screenY": 364}], "touches": []}, "targetCandidates": ["view.address", ".address", "/view/view[1]/view/view[1]/view[2]"], "timeStamp": 23124, "waitfor": 91, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "view", "command": "tap", "target": "view.address", "value": null, "path": "pages/address/address", "text": "北京市北京市西城区12", "eventData": {"detail": {"x": 176.4375, "y": 58.75}, "changedTouches": [{"clientX": 176.4375, "clientY": 58.75, "pageX": 176.4375, "pageY": 58.75, "screenX": 580.7999877929688, "screenY": 364}], "touches": [{"clientX": 176.4375, "clientY": 58.75, "pageX": 176.4375, "pageY": 58.75, "screenX": 580.7999877929688, "screenY": 364}]}, "targetCandidates": ["view.address", ".address", "/view/view[1]/view/view[1]/view[2]"], "timeStamp": 23124, "waitfor": 0, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "", "command": "scroll", "target": "/", "path": "pages/order/confirm", "eventData": {"scrollDetail": {"scrollTop": 34.400001525878906, "scrollLeft": 0}}, "targetCandidates": [], "timeStamp": 24335, "waitfor": 1211, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "button", "command": "touchstart", "target": "button.submit-btn", "value": null, "path": "pages/order/confirm", "text": "提交订单", "eventData": {"detail": {}, "changedTouches": [{"clientX": 317.2375183105469, "clientY": 805.9500122070312, "force": 1, "pageX": 317.2375183105469, "pageY": 840.3500366210938, "screenX": 721.6000366210938, "screenY": 1000}], "touches": [{"clientX": 317.2375183105469, "clientY": 805.9500122070312, "force": 1, "pageX": 317.2375183105469, "pageY": 840.3500366210938, "screenX": 721.6000366210938, "screenY": 1000}]}, "targetCandidates": ["button.submit-btn", ".submit-btn", "/view[1]/view[5]/button"], "timeStamp": 26608, "waitfor": 2273, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "button", "command": "touchend", "target": "button.submit-btn", "value": null, "path": "pages/order/confirm", "text": "提交订单", "eventData": {"detail": {}, "changedTouches": [{"clientX": 319.2000427246094, "clientY": 805.5999755859375, "pageX": 319.2000427246094, "pageY": 840, "screenX": 723.2000122070312, "screenY": 1000}], "touches": []}, "targetCandidates": ["button.submit-btn", ".submit-btn", "/view[1]/view[5]/button"], "timeStamp": 26704, "waitfor": 96, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "button", "command": "tap", "target": "button.submit-btn", "value": null, "path": "pages/order/confirm", "text": "提交订单", "eventData": {"detail": {"x": 319.2000427246094, "y": 840}, "changedTouches": [{"clientX": 317.2375183105469, "clientY": 805.9500122070312, "force": 1, "pageX": 317.2375183105469, "pageY": 840.3500366210938, "screenX": 721.6000366210938, "screenY": 1000}], "touches": [{"clientX": 317.2375183105469, "clientY": 805.9500122070312, "force": 1, "pageX": 317.2375183105469, "pageY": 840.3500366210938, "screenX": 721.6000366210938, "screenY": 1000}]}, "targetCandidates": ["button.submit-btn", ".submit-btn", "/view[1]/view[5]/button"], "timeStamp": 26705, "waitfor": 1, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"tagName": "", "command": "scroll", "target": "/", "path": "pages/order/confirm", "eventData": {"scrollDetail": {"scrollTop": 0, "scrollLeft": 0}}, "targetCandidates": [], "timeStamp": 27723, "waitfor": 1018, "desc": "", "commandType": "operate", "layer": "pageframe"}, {"timeStamp": 30016, "waitfor": 2293, "desc": "", "commandType": "operate", "layer": "native", "command": "confirmModal", "text": "确认支付"}, {"timeStamp": 34891, "waitfor": 4875, "desc": "", "commandType": "operate", "layer": "native", "command": "navigateLeft", "text": ""}, {"timeStamp": 35460, "waitfor": 569, "desc": "", "commandType": "operate", "layer": "native", "command": "navigateLeft", "text": ""}, {"timeStamp": 37405, "waitfor": 1945, "desc": "", "commandType": "operate", "layer": "native", "command": "switchTab", "text": "个人中心", "data": {"url": "pages/profile/profile"}}, {"timeStamp": 41309, "waitfor": 3904, "desc": "", "layer": "tool", "commandType": "operate", "command": "stopRecord"}]}