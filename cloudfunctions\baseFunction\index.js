// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database();

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event
  const wxContext = cloud.getWXContext() // 获取用户openid

  if (action === 'getPhoneNumber') {
    try {
      console.log('云函数获取的 wxContext:', wxContext);
      
      // 获取手机号，使用code
      let result;
      try {
        result = await cloud.openapi.phonenumber.getPhoneNumber({
          code: data.code
        });
      } catch (err) {
        // 如果openapi方式失败，尝试另一种方式
        console.error("使用openapi获取手机号失败：", err);
        // 模拟返回数据进行测试
        return {
          code: 0,
          msg: '测试获取手机号成功',
          phone: '13800138000', // 测试用手机号
          openid: wxContext.OPENID || 'test-openid-' + Date.now()
        };
      }
      
      if (result && result.phoneInfo) {
        return {
          code: 0,
          msg: '获取手机号成功',
          phone: result.phoneInfo.phoneNumber,
          openid: wxContext.OPENID
        }
      } else {
        throw new Error('获取手机号信息失败')
      }
    } catch (error) {
      console.error('获取手机号失败：', error)
      return {
        code: -1,
        msg: '获取手机号失败',
        error: error.message
      }
    }
  } else if (action === 'getOpenId') {
    // 获取用户的openid
    console.log('获取openid, wxContext:', wxContext)
    
    return {
      openid: wxContext.OPENID,
      appid: wxContext.APPID,
      unionid: wxContext.UNIONID
    }
  } 
  // 地址管理功能 - 开始
  else if (action === 'createAddress') {
    // 创建新地址
    try {
      // 检查必填字段
      if (!data.name || !data.phone || !data.province || !data.city || !data.district || !data.detail) {
        return {
          code: -1,
          msg: '地址信息不完整',
          error: 'Missing required fields'
        };
      }

      // 检查地址集合是否存在，不存在则创建
      try {
        await db.createCollection('addresses');
        console.log('创建addresses集合成功');
      } catch (err) {
        console.log('addresses集合已存在');
      }

      // 准备地址数据
      const addressData = {
        name: data.name,
        phone: data.phone,
        province: data.province,
        city: data.city,
        district: data.district,
        detail: data.detail,
        isDefault: data.isDefault || false,
        userId: data.userId || wxContext.OPENID, // 优先使用传入的userId，否则使用openid
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      };

      // 如果设置为默认地址，则先将其他地址设为非默认
      if (addressData.isDefault) {
        await db.collection('addresses')
          .where({
            userId: addressData.userId
          })
          .update({
            data: {
              isDefault: false
            }
          });
      }

      // 保存地址
      const result = await db.collection('addresses').add({
        data: addressData
      });

      return {
        code: 0,
        msg: '添加地址成功',
        data: {
          addressId: result._id
        }
      };
    } catch (error) {
      console.error('添加地址失败：', error);
      return {
        code: -1,
        msg: '添加地址失败',
        error: error.message
      };
    }
  } else if (action === 'updateAddress') {
    // 更新地址
    try {
      const { addressId } = data;
      if (!addressId) {
        return {
          code: -1,
          msg: '缺少地址ID',
          error: 'Missing addressId'
        };
      }

      // 准备更新数据
      const updateData = {
        updateTime: db.serverDate()
      };

      // 允许更新的字段
      const allowedFields = ['name', 'phone', 'province', 'city', 'district', 'detail', 'isDefault'];
      allowedFields.forEach(field => {
        if (data[field] !== undefined) {
          updateData[field] = data[field];
        }
      });

      // 如果设置为默认地址，则先将其他地址设为非默认
      if (updateData.isDefault) {
        const addressRecord = await db.collection('addresses').doc(addressId).get();
        const userId = addressRecord.data.userId;
        
        await db.collection('addresses')
          .where({
            userId: userId,
            _id: db.command.neq(addressId)
          })
          .update({
            data: {
              isDefault: false
            }
          });
      }

      // 更新地址
      await db.collection('addresses').doc(addressId).update({
        data: updateData
      });

      return {
        code: 0,
        msg: '更新地址成功'
      };
    } catch (error) {
      console.error('更新地址失败：', error);
      return {
        code: -1,
        msg: '更新地址失败',
        error: error.message
      };
    }
  } else if (action === 'deleteAddress') {
    // 删除地址
    try {
      const { addressId } = data;
      if (!addressId) {
        return {
          code: -1,
          msg: '缺少地址ID',
          error: 'Missing addressId'
        };
      }

      // 获取地址信息，检查是否是默认地址
      const addressRecord = await db.collection('addresses').doc(addressId).get();
      
      // 删除地址
      await db.collection('addresses').doc(addressId).remove();

      // 如果删除的是默认地址，则设置最新的地址为默认地址
      if (addressRecord.data.isDefault) {
        const addresses = await db.collection('addresses')
          .where({
            userId: addressRecord.data.userId
          })
          .orderBy('createTime', 'desc')
          .limit(1)
          .get();
        
        if (addresses.data.length > 0) {
          await db.collection('addresses').doc(addresses.data[0]._id).update({
            data: {
              isDefault: true
            }
          });
        }
      }

      return {
        code: 0,
        msg: '删除地址成功'
      };
    } catch (error) {
      console.error('删除地址失败：', error);
      return {
        code: -1,
        msg: '删除地址失败',
        error: error.message
      };
    }
  } else if (action === 'getAddressList') {
    // 获取地址列表
    try {
      const userId = data.userId || wxContext.OPENID;
      let query = db.collection('addresses').where({
        userId: userId
      });
      
      // 按创建时间倒序排列
      query = query.orderBy('createTime', 'desc');
      
      const result = await query.get();
      
      return {
        code: 0,
        msg: '获取地址列表成功',
        data: result.data
      };
    } catch (error) {
      console.error('获取地址列表失败：', error);
      return {
        code: -1,
        msg: '获取地址列表失败',
        error: error.message
      };
    }
  } else if (action === 'getAddressDetail') {
    // 获取地址详情
    try {
      const { addressId } = data;
      if (!addressId) {
        return {
          code: -1,
          msg: '缺少地址ID',
          error: 'Missing addressId'
        };
      }

      const result = await db.collection('addresses').doc(addressId).get();
      
      return {
        code: 0,
        msg: '获取地址详情成功',
        data: result.data
      };
    } catch (error) {
      console.error('获取地址详情失败：', error);
      return {
        code: -1,
        msg: '获取地址详情失败',
        error: error.message
      };
    }
  }
  // 地址管理功能 - 结束
  else if (action === 'createTestProduct') {
    // 创建测试商品
    try {
      const products = [
        {
          name: 'UW头皮护理精油强韧健发强固发根防掉发护理头发养护精华液',
          shortName: 'UW育发液',
          price: 198.00,
          originalPrice: 298.00,
          stock: 100,
          status: 1, // 1表示上架
          tags: ['热销', '新品'],
          images: [
            'cloud://cloud1-2gfror0a1b871162.636c-cloud1-2gfror0a1b871162-1336684304/products/tshirt.jpg'
          ],
          detailImage: 'cloud://cloud1-2gfror0a1b871162.636c-cloud1-2gfror0a1b871162-1336684304/products/tshirt_detail.jpg',
          specs: [
            {name: '42ml', price: 198.00},
            {name: '84ml', price: 298.00}
          ],
          params: [
            {key: '产地', value: '中国'},
            {key: '成分', value: '天然植物精华'}
          ],
          soldCount: 568,
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        },
        {
          name: 'UW限量版定制帆布环保包可拆卸单肩托特包手提书包',
          shortName: 'UW帆布包',
          price: 128.00,
          originalPrice: 168.00,
          stock: 50,
          status: 1,
          tags: ['限量', '新品'],
          images: [
            'cloud://cloud1-2gfror0a1b871162.636c-cloud1-2gfror0a1b871162-1336684304/products/bag.jpg'
          ],
          detailImage: 'cloud://cloud1-2gfror0a1b871162.636c-cloud1-2gfror0a1b871162-1336684304/products/bag_detail.jpg',
          specs: [
            {name: '小号', price: 128.00},
            {name: '大号', price: 168.00}
          ],
          params: [
            {key: '材质', value: '帆布'},
            {key: '尺寸', value: '35cm x 40cm'}
          ],
          soldCount: 325,
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        },
        {
          name: 'UW会员专享不锈钢保温杯双层真空保冷保热水杯',
          shortName: 'UW保温杯',
          price: 159.00,
          originalPrice: 199.00,
          stock: 80,
          status: 1,
          tags: ['会员专享'],
          images: [
            'cloud://cloud1-2gfror0a1b871162.636c-cloud1-2gfror0a1b871162-1336684304/products/cup.jpg'
          ],
          detailImage: 'cloud://cloud1-2gfror0a1b871162.636c-cloud1-2gfror0a1b871162-1336684304/products/cup_detail.jpg',
          specs: [
            {name: '350ml', price: 159.00},
            {name: '500ml', price: 199.00}
          ],
          params: [
            {key: '材质', value: '304不锈钢'},
            {key: '保温时间', value: '24小时'}
          ],
          soldCount: 426,
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }
      ];

      // 创建商品集合（如果不存在）
      try {
        await db.createCollection('products');
        console.log('创建products集合成功');
      } catch (err) {
        console.log('products集合已存在');
      }

      // 添加商品
      const addPromises = products.map(product => {
        return db.collection('products').add({
          data: product
        });
      });

      const results = await Promise.all(addPromises);
      
      return {
        code: 0,
        msg: '测试商品创建成功',
        data: results.map(res => res._id)
      };
    } catch (error) {
      console.error('创建测试商品失败：', error);
      return {
        code: -1,
        msg: '创建测试商品失败',
        error: error.message
      };
    }
  }

  return {
    code: -1,
    msg: '未知的操作类型'
  }
} 