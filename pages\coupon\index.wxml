<view class="container">
  <!-- 兑换码输入区域 -->
  <view class="exchange-section">
    <view class="input-box">
      <input 
        type="text" 
        placeholder="请输入U粉券兑换码" 
        value="{{exchangeCode}}"
        bindinput="onInputCode"
      />
      <button class="exchange-btn" bindtap="exchangeCoupon">兑换</button>
    </view>
  </view>

  <!-- U粉券列表 -->
  <view class="coupon-list">
    <view class="coupon-item {{item.isUsed === 'true' ? 'used' : ''}}" 
          wx:for="{{couponList}}" 
          wx:key="_id">
      <view class="left">
        <view class="amount">
          <text class="symbol">¥</text>
          <text class="number">{{item.amount}}</text>
        </view>
        <view class="condition">满{{item.minAmount}}元可用</view>
      </view>
      <view class="right">
        <view class="name">{{item.name}}</view>
        <view class="date">有效期至：{{item.endDate || '永久有效'}}</view>
        <view class="status">{{item.isUsed === 'true' ? '已使用' : '可使用'}}</view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty" wx:if="{{couponList.length === 0}}">
    <text>暂无U粉券</text>
  </view>
</view> 