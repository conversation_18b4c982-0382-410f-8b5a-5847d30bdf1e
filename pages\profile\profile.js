// 引入环境配置
const ENV = require('../../config/env');

// 不再重复初始化云环境
const app = getApp();

Page({
  data: {
    isLogin: false,
    userInfo: null,
    showAgreement: false,
    agreementContent: [
      '一、前言',
      '欢迎使用UW提供的服务。',
      '本协议规定了您使用我们的服务的条款和条件。',
      '',
      '二、服务的提供',
      '1. 我们保留随时更改、暂停或终止服务的权利。',
      '2. 我们可能会不时更新服务内容。',
      '',
      '三、账户使用',
      '1. 您负责维护账户安全。',
      '2. 不得将账户转让给他人。',
      '',
      '四、内容使用',
      '1. 遵守适用的法律和规定。',
      '2. 不得进行非法活动。',
      '',
      '五、知识产权',
      '1. 我们拥有服务的所有知识产权。',
      '2. 未经同意不得复制或分发内容。',
      '',
      '六、免责声明',
      '服务按"现状"提供，不提供任何保证。',
      '',
      '七、协议修改',
      '我们保留随时修改本协议的权利。',
      '',
      '八、终止',
      '我们有权随时终止服务或账户。',
      '',
      '九、法律适用',
      '本协议适用中国大陆法律。',
      '',
      '[UW]',
      '2023年12月17日'
    ],
    showService: false,
    defaultAvatar: '/images/默认头像.png',
    showServiceQR: false
  },

  onLoad() {
    // 监听登录状态变化
    app.globalData.eventBus.on('loginStateChanged', this.handleLoginStateChanged);
    
    // 初始化检查登录状态
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.isLogin) {
      this.setData({
        isLogin: true,
        userInfo: userInfo
      });
    }
  },

  onUnload() {
    // 取消监听
    app.globalData.eventBus.off('loginStateChanged', this.handleLoginStateChanged);
  },

  handleLoginStateChanged(data) {
    this.setData({
      isLogin: data.isLogin,
      userInfo: data.userInfo
    });
  },

  generateUserId() {
    return 'UW' + Math.floor(10000 + Math.random() * 90000);
  },

  getPhoneNumber(e) {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      console.log('[登录] 开始获取手机号码');
      wx.showLoading({ title: '登录中...' });
      
      // 检查云环境是否可用
      if (!wx.cloud || !wx.cloud.callFunction) {
        console.error('[登录] 云环境未初始化');
        wx.hideLoading();
        wx.showModal({
          title: '登录失败',
          content: '云服务不可用，无法完成登录。请确保网络连接正常并有足够的访问权限。',
          showCancel: false
        });
        return;
      }
      
      wx.cloud.callFunction({
        name: 'baseFunction',
        data: {
          action: 'getPhoneNumber',
          data: {
            code: e.detail.code
          }
        }
      }).then(res => {
        console.log('[登录] 获取手机号成功:', res);
        if (res.result && res.result.phone) {
          // 记录openid
          console.log('[登录] 用户openid:', res.result.openid);
          const openid = res.result.openid;
          
          // 使用wx.cloud创建数据库实例而不是假设全局db可用
          if (!wx.cloud.database) {
            wx.hideLoading();
            wx.showModal({
              title: '登录失败',
              content: '云数据库服务不可用，无法完成登录。',
              showCancel: false
            });
            return;
          }
          
          const db = wx.cloud.database();
          const userId = this.generateUserId();
          const nickName = 'U粉' + Math.floor(10000 + Math.random() * 90000);
          
          // 先查询是否存在用户
          db.collection('users').where({
            _openid: openid
          }).get().then(userRes => {
            console.log('[登录] 查询用户结果:', userRes);
            
            if (userRes.data && userRes.data.length > 0) {
              // 用户已存在，更新信息
              const user = userRes.data[0];
              console.log('[登录] 已存在用户:', user);
              
              db.collection('users').doc(user._id).update({
                data: {
                  phone: res.result.phone,
                  updateTime: db.serverDate()
                }
              }).then(() => {
                this.loginSuccess({
                  ...user,
                  phone: res.result.phone,
                  dbUserId: user._id
                });
              }).catch(updateErr => {
                console.error('[登录] 更新用户失败:', updateErr);
                // 虽然更新失败，但用户仍然存在，仍可登录
                this.loginSuccess({
                  ...user,
                  phone: res.result.phone,
                  dbUserId: user._id
                });
              });
            } else {
              // 新用户，创建记录
              console.log('[登录] 创建新用户:', {nickName, phone: res.result.phone, userId});
              
              db.collection('users').add({
                data: {
                  nickName: nickName,
                  phone: res.result.phone,
                  avatarUrl: this.data.defaultAvatar,
                  userId: userId,
                  gender: 1,
                  age: '',
                  createTime: db.serverDate(),
                  updateTime: db.serverDate()
                }
              }).then(addRes => {
                console.log('[登录] 创建用户成功:', addRes);
                this.loginSuccess({
                  _id: addRes._id,
                  nickName: nickName,
                  phone: res.result.phone,
                  avatarUrl: this.data.defaultAvatar,
                  userId: userId,
                  gender: 1,
                  age: '',
                  _openid: openid,
                  dbUserId: addRes._id
                });
              }).catch(err => {
                console.error('[登录] 创建用户失败:', err);
                wx.hideLoading();
                wx.showModal({
                  title: '创建用户失败',
                  content: '无法创建用户账号，请稍后再试。',
                  showCancel: false
                });
              });
            }
          }).catch(err => {
            console.error('[登录] 查询用户失败:', err);
            wx.hideLoading();
            wx.showModal({
              title: '查询用户失败',
              content: '无法查询用户信息，请稍后再试。',
              showCancel: false
            });
          });
        } else {
          wx.hideLoading();
          wx.showModal({
            title: '登录失败',
            content: '获取手机号码失败，请稍后再试。',
            showCancel: false
          });
        }
      }).catch(err => {
        console.error('[登录] 获取手机号失败:', err);
        wx.hideLoading();
        wx.showModal({
          title: '登录失败',
          content: '调用云函数失败，请检查网络连接或稍后再试。',
          showCancel: false
        });
      });
    } else {
      console.log('[用户] 用户拒绝授权手机号码:', e.detail.errMsg);
    }
  },

  loginSuccess(userInfo) {
    // 存储到本地
    wx.setStorageSync('userInfo', {
      ...userInfo,
      isLogin: true
    });
    
    // 更新页面状态
    this.setData({
      isLogin: true,
      userInfo: userInfo
    });
    
    wx.hideLoading();
    wx.showToast({
      title: '登录成功',
      icon: 'success'
    });
    
    console.log('[用户] 登录流程完成');
  },

  goToUserInfo() {
    if (!this.data.isLogin) return
    wx.navigateTo({
      url: '/pages/profile/userInfo/index'
    })
  },

  onShow() {
    // 每次页面显示时检查更新用户信息
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({
        isLogin: true,
        userInfo
      })
    }
    // 确保在个人中心页面不显示购物车徽标
    wx.removeTabBarBadge({
      index: 2
    }).catch(() => {
      // 忽略可能的错误
    });
  },

  viewAllOrders() {
    if (!this.data.isLogin) return this.goToUserInfo()
    wx.navigateTo({
      url: '/pages/order/list'
    })
  },

  goToOrders(e) {
    if (!this.data.isLogin) return this.goToUserInfo()
    const type = e.currentTarget.dataset.type
    wx.navigateTo({
      url: `/pages/order/list?type=${type}`
    })
  },

  goToCoupons() {
    if (!this.data.isLogin) return this.goToUserInfo();
    wx.navigateTo({
      url: '/pages/coupon/index'
    });
  },

  goToAddress() {
    if (!this.data.isLogin) return this.goToUserInfo();
    wx.navigateTo({
      url: '/pages/address/address'
    });
  },

  contactService() {
    this.setData({
      showService: true
    });
  },

  hideService() {
    this.setData({
      showService: false
    });
  },

  goToAgreement() {
    this.setData({
      showAgreement: true
    });
  },

  hideAgreement() {
    this.setData({
      showAgreement: false
    });
  },

  agreeAgreement() {
    this.hideAgreement();
    // 处理同意逻辑
  },

  goToAbout() {
    wx.navigateTo({
      url: '/pages/about/index'
    })
  },

  goToBenefits() {
    if (!this.data.isLogin) return this.goToUserInfo();
    wx.navigateTo({
      url: '/pages/member/benefits/index'
    });
  },

  goToTrial() {
    if (!this.data.isLogin) return this.goToUserInfo();
    wx.navigateTo({
      url: '/pages/member/trial/index'
    });
  },

  showServiceQR() {
    this.setData({
      showServiceQR: true
    });
  },

  hideServiceQR() {
    this.setData({
      showServiceQR: false
    });
  },
}) 