const uploadFile = (cloudPath, filePath) => {
  return new Promise((resolve, reject) => {
    wx.cloud.uploadFile({
      cloudPath,
      filePath,
      success: res => {
        resolve(res.fileID);
      },
      fail: err => {
        reject(err);
      }
    });
  });
};

const uploadFiles = (files) => {
  return Promise.all(files.map(file => {
    return uploadFile(file.cloudPath, file.filePath);
  }));
};

module.exports = {
  uploadFile,
  uploadFiles
}; 