/* 移除徽标相关样式 */

.container {
  min-height: 100vh;
  padding-bottom: 120rpx;
  background: #f8f8f8;
}

.product-images image {
  width: 100%;
  display: block;
}

.product-info {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.price {
  color: #ff4d4f;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.desc {
  font-size: 28rpx;
  color: #666;
}

.specs-section,
.quantity-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.specs-list {
  display: flex;
  gap: 20rpx;
}

.spec-item {
  padding: 10rpx 30rpx;
  border: 2rpx solid #ddd;
  border-radius: 30rpx;
  font-size: 26rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.spec-item.active {
  border-color: #987;
  color: #987;
  background: rgba(152, 136, 119, 0.1);
}

.spec-size {
  font-size: 28rpx;
  font-weight: 500;
}

.spec-price {
  font-size: 24rpx;
  color: #ff4d4f;
}

.spec-item.active .spec-price {
  color: #987;
}

.quantity-control {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.minus,
.plus {
  width: 60rpx;
  height: 60rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.number {
  font-size: 28rpx;
  min-width: 60rpx;
  text-align: center;
}

/* 底部操作栏样式 */
.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  transition: transform 0.3s ease;
  transform: translateY(0);
}

.bottom-bar.hidden {
  transform: translateY(100%);
}

.cart-btn,
.buy-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.cart-btn {
  background: rgba(152, 136, 119, 0.1);
  color: #987;
  margin-right: 20rpx;
}

.buy-btn {
  background: #987;
  color: #fff;
} 