<view class="container">
  <!-- 欢迎动画 -->
  <view class="welcome-animation {{showWelcome ? 'show' : ''}}" bindtap="enterMainPage">
    <view class="welcome-box">
      <view class="welcome-text hi {{hideHi ? 'hide' : ''}}">hi！</view>
      <view class="welcome-text message {{showMessage ? 'show' : ''}}">
        <view class="message-content">欢迎您加入UW新品体验官！</view>
      </view>
    </view>
  </view>

  <!-- 页面主体内容 -->
  <view class="content {{showContent ? 'show' : ''}}">
    <view class="form-bg">
      <view class="trial-form">
        <view class="form-header">
          
          <view class="title-group">
            <text class="title">UW产品体验官申请</text>
            <text class="subtitle">填写信息 开启体验之旅</text>
          </view>
        </view>
        <view class="form-item">
          <text class="label">姓名</text>
          <input 
            class="input" 
            type="text" 
            placeholder="请输入您的姓名" 
            bindinput="onNameInput"
            value="{{name}}"
            cursor-spacing="100"
            catchtap="stopPropagation"
          />
        </view>

        <view class="form-item">
          <text class="label">手机号</text>
          <input 
            class="input" 
            type="number" 
            maxlength="11" 
            placeholder="请输入您的手机号" 
            bindinput="onPhoneInput"
            value="{{phone}}"
            cursor-spacing="100"
            catchtap="stopPropagation"
          />
        </view>

        <view class="form-item">
          <text class="label">性别</text>
          <view class="radio-group">
            <view class="radio-item {{gender === 1 ? 'active' : ''}}" bindtap="selectGender" data-gender="1">
              <text class="radio-text">男</text>
            </view>
            <view class="radio-item {{gender === 2 ? 'active' : ''}}" bindtap="selectGender" data-gender="2">
              <text class="radio-text">女</text>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="label">年龄</text>
          <input 
            class="input" 
            type="number" 
            maxlength="3" 
            placeholder="请输入您的年龄" 
            bindinput="onAgeInput"
            value="{{age}}"
            cursor-spacing="100"
            catchtap="stopPropagation"
          />
        </view>
        
        <view class="form-item">
          <text class="label">职业</text>
          <input 
            class="input" 
            type="text" 
            placeholder="请输入您的职业" 
            bindinput="onOccupationInput"
            value="{{occupation}}"
            cursor-spacing="100"
            catchtap="stopPropagation"
          />
        </view>

        <view class="form-item upload-item">
          <text class="label">购买凭证</text>
          <view class="upload-area" bindtap="chooseImage" wx:if="{{!receipt}}">
            <view class="upload-icon">+</view>
            <text class="upload-text">点击上传购买凭证截图</text>
          </view>
          <view class="preview-area" wx:else>
            <image class="preview-image" src="{{receipt}}" mode="aspectFit"/>
            <view class="delete-btn" catchtap="deleteImage">×</view>
          </view>
        </view>

        <view class="form-item agreement-check">
          <view class="checkbox {{agreed ? 'checked' : ''}}" bindtap="toggleAgreement">
            <view class="check-inner"></view>
          </view>
          <view class="agreement-text" bindtap="showAgreementModal">
            我已阅读并同意<text class="link">《UW产品体验官招募协议》</text>
          </view>
        </view>

        <button class="submit-btn {{!agreed ? 'disabled' : ''}}" bindtap="submitForm">提交申请</button>
      </view>
    </view>

    <!-- 活动规则 -->
    <view class="rules-section {{showContent ? 'show' : ''}}">
      <view class="rules-bg">
        <view class="rules-header">
          
          <view class="title-group">
            <text class="title">U30养发计划</text>
            <text class="subtitle">健康从"头"开始 全程追踪 购买无忧</text>
          </view>
        </view>
        
        <view class="rules-content">
          <view class="rule-group">
            <view class="group-header">
              <text class="number">01</text>
              <text class="group-title">参与条件</text>
            </view>
            <text class="rule-text">参与活动需在各大平台凭借"UW体验官"暗号找店铺客服领取专属优惠卷后购买【UW强韧健发精华液】42ml</text>
          </view>
          
          <view class="rule-group">
            <view class="group-header">
              <text class="number">02</text>
              <text class="group-title">参与规则</text>
            </view>
            <view class="steps">
              <view class="step-item">
                <text class="step-num">STEP 1</text>
                <text class="step-text">购买产品后，客户仔细阅读体验官招募协议后，确认参与活动</text>
              </view>
              <view class="step-item">
                <text class="step-num">STEP 2</text>
                <text class="step-text">确认参与后，客户需要按照养发师的给出的使用频率，严格按时拍照反馈打卡</text>
              </view>
              <view class="step-item">
                <text class="step-num">STEP 3</text>
                <text class="step-text">坚持使用至体验装使用完，体验结束后平台支付金额将全额退款！</text>
              </view>
            </view>
          </view>
          
          <view class="notice">
            <text class="notice-text">*使用过程，如非特殊情况（过敏等），中途放弃打卡超过2次及2次以上即视为退出活动，不享受后续权益。</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 协议弹窗 -->
  <view class="agreement-modal {{showAgreement ? 'show' : ''}}" catchtouchmove="preventScroll">
    <view class="modal-mask" bindtap="closeAgreement"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>UW体验官招募协议</text>
        <view class="close" bindtap="closeAgreement">×</view>
      </view>
      <view class="modal-body">
        <image 
          wx:for="{{agreementImages}}" 
          wx:key="*this"
          src="{{item}}" 
          mode="widthFix" 
          class="agreement-image"
          lazy-load
        />
      </view>
      <view class="modal-footer">
        <button class="disagree-btn" bindtap="closeAgreement">不同意</button>
        <button class="agree-btn" bindtap="confirmSubmit">同意并提交</button>
      </view>
    </view>
  </view>
</view> 