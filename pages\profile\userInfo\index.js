Page({
  data: {
    userInfo: null
  },

  onLoad() {
    const userInfo = wx.getStorageSync('userInfo')
    this.setData({
      userInfo
    })
  },

  // 修改头像
  changeAvatar() {
    wx.chooseImage({
      count: 1,
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0]
        const userInfo = { ...this.data.userInfo, avatarUrl: tempFilePath }
        this.updateUserInfo(userInfo)
      },
      fail: () => {
        wx.showToast({
          title: '请授权相册权限',
          icon: 'none'
        })
      }
    })
  },

  // 修改昵称
  changeNickname() {
    wx.showModal({
      title: '修改昵称',
      editable: true,
      placeholderText: '请输入新昵称',
      success: (res) => {
        if (res.confirm && res.content) {
          const userInfo = { ...this.data.userInfo, nickName: res.content }
          this.updateUserInfo(userInfo)
        }
      }
    })
  },

  // 修改性别
  changeGender() {
    wx.showActionSheet({
      itemList: ['男', '女'],
      success: (res) => {
        const gender = res.tapIndex + 1
        const userInfo = { ...this.data.userInfo, gender }
        this.updateUserInfo(userInfo)
      }
    })
  },

  // 修改年龄
  changeAge() {
    wx.showModal({
      title: '修改年龄',
      editable: true,
      placeholderText: '请输入年龄',
      success: (res) => {
        if (res.confirm && res.content) {
          const age = parseInt(res.content)
          if (isNaN(age) || age < 0 || age > 150) {
            wx.showToast({
              title: '请输入有效年龄',
              icon: 'none'
            })
            return
          }
          const userInfo = { ...this.data.userInfo, age }
          this.updateUserInfo(userInfo)
        }
      }
    })
  },

  // 更新用户信息
  updateUserInfo(userInfo) {
    const db = wx.cloud.database();
    
    // 复制用户信息，排除_openid、_id等不可更新字段
    const { nickName, avatarUrl, gender, age, phone, userId } = userInfo;
    const updateData = {
      nickName,
      avatarUrl,
      gender,
      age,
      phone,
      userId,
      updateTime: db.serverDate()
    };
    
    // 更新云数据库
    db.collection('users')
      .where({
        _openid: userInfo._openid
      })
      .update({
        data: updateData
      })
      .then(() => {
        // 更新本地存储
        wx.setStorageSync('userInfo', userInfo);
        this.setData({ userInfo });
        wx.showToast({
          title: '修改成功',
          icon: 'success'
        });
      })
      .catch(err => {
        console.error('[用户] 更新信息失败:', err);
        wx.showToast({
          title: '修改失败',
          icon: 'error'
        });
      });
  }
}) 