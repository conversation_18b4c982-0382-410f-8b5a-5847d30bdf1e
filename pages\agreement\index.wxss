.container {
  padding: 30rpx;
  min-height: 100vh;
  background: #fff;
}

.content {
  margin-bottom: 120rpx;
}

.text-line {
  display: block;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
  display: flex;
  gap: 20rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background: #987;
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
}

.btn.cancel {
  background: #f5f5f5;
  color: #333;
} 