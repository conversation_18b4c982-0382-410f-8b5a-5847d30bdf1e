<!--pages/cart/index.wxml-->
<view class="container">
  <!-- 加载中 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="weui-loading"></view>
  </view>

  <!-- 购物车列表 -->
  <view class="cart-list" wx:if="{{!loading && cartList.length > 0}}">
    <view class="cart-item" wx:for="{{cartList}}" wx:key="_id">
      <!-- 选择框 -->
      <view class="checkbox" bindtap="toggleSelect" data-id="{{item._id}}">
        <view class="check-inner {{item.selected ? 'selected' : ''}}"></view>
      </view>
      
      <!-- 商品信息 -->
      <image class="product-image" src="{{item.product.image}}" mode="aspectFill"/>
      <view class="product-info">
        <view class="product-name">{{item.product.name}}</view>
        <view class="product-spec">{{item.specs.name}}</view>
        <view class="price-quantity">
          <view class="quantity-control">
            <text class="minus" bindtap="updateQuantity" data-id="{{item._id}}" data-type="minus">-</text>
            <text class="number">{{item.quantity}}</text>
            <text class="plus" bindtap="updateQuantity" data-id="{{item._id}}" data-type="plus">+</text>
          </view>
          <text class="price">¥{{item.product.price}}</text>
        </view>
      </view>
      
      <!-- 删除按钮 -->
      <view class="delete" bindtap="deleteCartItem" data-id="{{item._id}}">删除</view>
    </view>
  </view>

  <!-- 空购物车 -->
  <view class="empty" wx:if="{{!loading && cartList.length === 0}}">
    <text>购物车是空的</text>
    <button class="go-shop" bindtap="goToShop">去逛逛</button>
  </view>

  <!-- 底部结算栏 -->
  <view class="bottom-bar" wx:if="{{!loading && cartList.length > 0}}">
    <view class="select-all" bindtap="toggleSelectAll">
      <view class="checkbox {{allSelected ? 'selected' : ''}}"></view>
      <text>全选</text>
    </view>
    <view class="total">
      合计：<text class="price">¥{{totalPrice}}</text>
    </view>
    <button class="checkout-btn" bindtap="checkout">结算({{selectedCount}})</button>
  </view>
</view>