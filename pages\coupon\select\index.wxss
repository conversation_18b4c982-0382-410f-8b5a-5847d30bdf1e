.container {
  min-height: 100vh;
  background: #f8f8f8;
  padding: 20rpx;
}

.coupon-list {
  padding: 20rpx;
}

.coupon-item {
  display: flex;
  background: #fff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  opacity: 0.8;
}

.coupon-item.selected {
  opacity: 1;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.left {
  background: linear-gradient(135deg, #9F8C84, #988075);
  color: #fff;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-width: 200rpx;
  position: relative;
  overflow: hidden;
}

.left::after {
  content: '';
  position: absolute;
  right: -10rpx;
  top: 0;
  bottom: 0;
  width: 20rpx;
  background: radial-gradient(circle at left, transparent 10rpx, #fff 10rpx);
}

.amount {
  display: flex;
  align-items: baseline;
}

.symbol {
  font-size: 28rpx;
  opacity: 0.9;
}

.number {
  font-size: 48rpx;
  font-weight: bold;
  margin-left: 4rpx;
}

.condition {
  font-size: 24rpx;
  margin-top: 8rpx;
  opacity: 0.9;
}

.right {
  flex: 1;
  padding: 20rpx 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  background: #fff;
}

.coupon-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.date {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.empty {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

.coupon-item::before {
  content: '';
  position: absolute;
  left: 190rpx;
  top: 0;
  bottom: 0;
  border-left: 2rpx dashed #eee;
} 