import request from '../utils/request';

const API = {
  LOGIN: '/api/user/login',
  USER_INFO: '/api/user/info',
  UPDATE_PROFILE: '/api/user/update'
};

const userService = {
  // 登录服务
  login: async function(data) {
    try {
      // 获取微信登录凭证
      const loginResult = await wx.login();
      return request.post(API.LOGIN, {
        code: loginResult.code,
        ...data
      });
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  },

  // 获取用户信息
  getUserInfo: async function() {
    try {
      return await request.get(API.USER_INFO);
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  },

  // 更新用户信息
  updateUserProfile: async function(profileData) {
    try {
      return await request.post(API.UPDATE_PROFILE, profileData);
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw error;
    }
  }
};

export default userService; 