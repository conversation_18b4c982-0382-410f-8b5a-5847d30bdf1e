// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command
const $ = db.command.aggregate

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  console.log(`执行交易功能: ${action}`, data)

  // 根据action参数执行不同操作
  switch (action) {
    // 购物车相关操作
    case 'addToCart':
      return await addToCart(data, openid)
    case 'updateCartItem':
      return await updateCartItem(data, openid)
    case 'removeCartItem':
      return await removeCartItem(data, openid)
    case 'getCartList':
      return await getCartList(openid)
    // 商品库存相关操作
    case 'checkStock':
      return await checkStock(data)
    default:
      return {
        code: -1,
        msg: '未知的操作类型'
      }
  }
}

// 添加商品到购物车
async function addToCart(data, openid) {
  try {
    // 检查购物车集合是否存在
    try {
      await db.createCollection('cart')
      console.log('创建cart集合成功')
    } catch (err) {
      console.log('cart集合已存在')
    }

    const { productId, quantity = 1 } = data

    // 检查商品是否存在
    const productRes = await db.collection('products').doc(productId).get()
    if (!productRes.data) {
      return {
        code: -1,
        msg: '商品不存在'
      }
    }

    // 检查库存
    if (productRes.data.stock < quantity) {
      return {
        code: -1,
        msg: '商品库存不足'
      }
    }

    // 检查购物车中是否已有该商品
    const cartRes = await db.collection('cart').where({
      productId,
      _openid: openid,
      isDeleted: false
    }).get()

    if (cartRes.data && cartRes.data.length > 0) {
      // 更新数量
      const newQuantity = cartRes.data[0].quantity + quantity
      
      // 再次检查库存
      if (productRes.data.stock < newQuantity) {
        return {
          code: -1,
          msg: '商品库存不足'
        }
      }

      await db.collection('cart').doc(cartRes.data[0]._id).update({
        data: {
          quantity: newQuantity,
          updateTime: db.serverDate()
        }
      })

      return {
        code: 0,
        msg: '商品已更新到购物车'
      }
    } else {
      // 添加新商品到购物车
      await db.collection('cart').add({
        data: {
          productId,
          quantity,
          price: productRes.data.price,
          name: productRes.data.name,
          image: productRes.data.images[0],
          _openid: openid,
          isDeleted: false,
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }
      })

      return {
        code: 0,
        msg: '商品已添加到购物车'
      }
    }
  } catch (error) {
    console.error('添加到购物车失败:', error)
    return {
      code: -1,
      msg: '添加到购物车失败',
      error: error.message
    }
  }
}

// 更新购物车商品数量
async function updateCartItem(data, openid) {
  try {
    const { cartId, quantity } = data

    if (quantity <= 0) {
      // 如果数量为0或负数，执行删除
      return await removeCartItem({ cartId }, openid)
    }

    // 获取购物车项
    const cartRes = await db.collection('cart').doc(cartId).get()
    if (!cartRes.data) {
      return {
        code: -1,
        msg: '购物车项不存在'
      }
    }

    // 检查商品库存
    const productRes = await db.collection('products').doc(cartRes.data.productId).get()
    if (!productRes.data) {
      return {
        code: -1,
        msg: '商品不存在'
      }
    }

    if (productRes.data.stock < quantity) {
      return {
        code: -1,
        msg: '商品库存不足'
      }
    }

    // 更新数量
    await db.collection('cart').doc(cartId).update({
      data: {
        quantity,
        updateTime: db.serverDate()
      }
    })

    return {
      code: 0,
      msg: '购物车已更新'
    }
  } catch (error) {
    console.error('更新购物车失败:', error)
    return {
      code: -1,
      msg: '更新购物车失败',
      error: error.message
    }
  }
}

// 从购物车中删除商品
async function removeCartItem(data, openid) {
  try {
    const { cartId } = data

    // 软删除，将isDeleted设为true
    await db.collection('cart').doc(cartId).update({
      data: {
        isDeleted: true,
        updateTime: db.serverDate()
      }
    })

    return {
      code: 0,
      msg: '商品已从购物车中删除'
    }
  } catch (error) {
    console.error('从购物车删除失败:', error)
    return {
      code: -1,
      msg: '从购物车删除失败',
      error: error.message
    }
  }
}

// 获取购物车列表
async function getCartList(openid) {
  try {
    const cartRes = await db.collection('cart')
      .where({
        _openid: openid,
        isDeleted: false
      })
      .orderBy('createTime', 'desc')
      .get()

    return {
      code: 0,
      msg: '获取购物车成功',
      data: cartRes.data
    }
  } catch (error) {
    console.error('获取购物车失败:', error)
    return {
      code: -1,
      msg: '获取购物车失败',
      error: error.message
    }
  }
}

// 检查商品库存
async function checkStock(data) {
  try {
    const { items } = data
    const stockPromises = items.map(item => {
      return db.collection('products').doc(item.productId).get()
    })

    const stockResults = await Promise.all(stockPromises)
    
    const checkResults = stockResults.map((res, index) => {
      const product = res.data
      const requestQuantity = items[index].quantity
      
      return {
        productId: items[index].productId,
        name: product ? product.name : '未知商品',
        requestQuantity,
        availableStock: product ? product.stock : 0,
        isEnough: product && product.stock >= requestQuantity
      }
    })

    const isAllAvailable = checkResults.every(item => item.isEnough)

    return {
      code: 0,
      msg: isAllAvailable ? '库存充足' : '部分商品库存不足',
      data: {
        isAllAvailable,
        items: checkResults
      }
    }
  } catch (error) {
    console.error('检查库存失败:', error)
    return {
      code: -1,
      msg: '检查库存失败',
      error: error.message
    }
  }
} 