<view class="container">
  <view class="loading" wx:if="{{loading}}">加载中...</view>
  
  <view class="product-list" wx:else>
    <view class="product-item" 
          wx:for="{{productList}}" 
          wx:key="_id"
          bindtap="goToDetail"
          data-id="{{item._id}}">
      <common-image 
        src="{{item.image}}" 
        mode="aspectFill"
        custom-class="product-image"
        image-type="PRODUCT"
      />
      <view class="product-info">
        <view class="product-name">{{item.name}}</view>
        <view class="product-price">¥{{item.price}}</view>
      </view>
    </view>
  </view>
</view>

