.container {
  padding: 20rpx;
}

.loading {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
}

.product-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.product-item {
  background: #fff;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.product-image {
  width: 100%;
  height: 300rpx;
}

.product-info {
  padding: 20rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.product-price {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: bold;
}
