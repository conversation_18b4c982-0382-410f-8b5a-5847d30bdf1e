const cloud = wx.cloud;
import paymentService from '../../utils/paymentService';
import orderService from '../../utils/orderService';
import wxpayService from '../../utils/wxpayService';

const db = wx.cloud.database()

Page({
  data: {
    currentTab: 0,
    orderList: [],
    allOrderData: {},  // 存储所有状态的订单数据
    countdownTimers: {},  // 存储倒计时定时器
    // 定义tab和订单状态的映射关系
    tabStatusMap: {
      0: [0],          // 待付款：status = 0
      1: [1],          // 待发货：status = 1
      2: [2],          // 已发货：status = 2
      3: [3, 5, 6]     // 退款/售后：status = 3(已完成) 或 5(退款中) 或 6(已退款)
    },
    statusText: {
      0: '待付款',
      1: '待发货',
      2: '已发货',
      3: '已完成',
      4: '已取消',
      5: '退款中',
      6: '已退款'
    }
  },

  // 取消订单
  async cancelOrder(e) {
    const orderId = e.currentTarget.dataset.id;
    
    try {
      wx.showLoading({ title: '处理中' });
      
      // 更新订单状态为已取消(4)
      await db.collection('orders').doc(orderId).update({
        data: {
          status: 4,
          statusHistory: db.command.push({
            status: 4,
            time: db.serverDate(),
            desc: '用户取消订单'
          }),
          updateTime: db.serverDate()
        }
      });

      wx.hideLoading();
      wx.showToast({
        title: '取消成功',
        icon: 'success'
      });

      // 更新本地订单状态，无需重新请求
      const newOrderList = this.data.orderList.map(order => {
        if (order._id === orderId) {
          return {
            ...order,
            status: 4
          };
        }
        return order;
      });
      
      this.setData({
        orderList: newOrderList
      });

    } catch (err) {
      console.error('[订单] 取消失败:', err);
      wx.hideLoading();
      wx.showToast({
        title: '取消失败',
        icon: 'error'
      });
    }
  },

  // 检查订单是否需要自动取消
  checkOrderTimeout() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) return;
    
    const _ = db.command;
    const now = new Date();
    const fiveMinutesAgo = new Date(now - 5 * 60 * 1000);
    
    // 查询5分钟前创建且未支付的订单
    db.collection('orders')
      .where({
        _openid: userInfo._openid,
        status: 0,
        createTime: _.lte(fiveMinutesAgo)
      })
      .get()
      .then(res => {
        // 批量更新这些订单状态为已取消
        const updatePromises = res.data.map(order => {
          return wx.cloud.callFunction({
            name: 'cancelOrder',
            data: {
              orderId: order._id,
              type: 'auto'
            }
          });
        });
        
        return Promise.all(updatePromises);
      })
      .then(() => {
        if (this.data.currentTab === 0) {
          this.getOrderList();
        }
      })
      .catch(err => {
        console.error('自动取消订单失败：', err);
      });
  },

  // 检查订单是否需要自动确认收货
  checkOrderAutoConfirm() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) return;
    
    const _ = db.command;
    const now = new Date();
    const sevenDaysAgo = new Date(now - 7 * 24 * 60 * 60 * 1000);
    
    // 查询7天前发货且未确认收货的订单
    db.collection('orders')
      .where({
        _openid: userInfo._openid,
        status: 2,  // 已发货状态
        updateTime: _.lte(sevenDaysAgo)
      })
      .get()
      .then(res => {
        // 批量更新这些订单状态为已完成
        const updatePromises = res.data.map(order => {
          return wx.cloud.callFunction({
            name: 'confirmOrder',
            data: {
              orderId: order._id,
              type: 'auto'
            }
          });
        });
        
        return Promise.all(updatePromises);
      })
      .then(() => {
        if (this.data.currentTab === 2) {
          // 使用 setData 直接更新状态，而不是重新获取列表
          const newOrderList = this.data.orderList.map(order => {
            if (order.status === 2) {
              return { ...order, status: 3 };
            }
            return order;
          });
          this.setData({ orderList: newOrderList });
        }
      })
      .catch(err => {
        console.error('自动确认收货失败：', err);
      });
  },

  // 页面加载时只调用一次
  onLoad(options) {
    // 如果从个人中心跳转来，设置对应的tab
    if (options.type) {
      this.setData({
        currentTab: Number(options.type) - 1
      });
    }
    // 启动定时检查
    this.checkOrderTimeout();
    this.checkOrderAutoConfirm();
    this.autoCheckInterval = setInterval(() => {
      this.checkOrderTimeout();
      this.checkOrderAutoConfirm();
    }, 60000);  // 每分钟检查一次
  },

  // 页面显示时调用
  onShow() {
    // 只有在没有任何订单数据时才重新获取
    if (Object.keys(this.data.allOrderData).length === 0) {
      this.getOrderList();
    }
  },

  // 切换标签
  switchTab(e) {
    const index = e.currentTarget.dataset.index;
    if (this.data.currentTab === index) return;
    
    // 如果已经有缓存数据，直接使用
    if (this.data.allOrderData[index]) {
      this.setData({
        currentTab: index,
        orderList: this.data.allOrderData[index]
      });
      return;
    }
    
    // 只有在没有缓存数据时才请求
    this.setData({ currentTab: index }, () => {
      this.getOrderList();
    });
  },

  // 获取订单列表
  async getOrderList() {
    try {
      const { currentTab } = this.data;
      const status = this.data.tabStatusMap[currentTab];
      const _ = db.command;
      
      // 获取用户信息
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo || !userInfo.dbUserId) {
        console.error('[订单列表] 用户未登录');
        return;
      }

      console.log('[订单列表] 开始获取订单:', {
        当前Tab: currentTab,
        状态值: status,
        用户ID: userInfo.dbUserId
      });

      // 构建查询条件
      const query = {
        userId: userInfo.dbUserId,
        status: _.in(status),
        isDeleted: _.or([false, _.exists(false)])
      };

      const orderList = await db.collection('orders')
        .where(query)
        .orderBy('createTime', 'desc')
        .get();

      console.log('[订单列表] 获取成功:', {
        订单数量: orderList.data.length,
        订单列表: orderList.data.map(order => ({
          订单号: order.orderNo,
          状态: order.status,
          商品列表: order.products
        }))
      });

      this.setData({
        orderList: orderList.data
      });

    } catch (err) {
      console.error('[订单列表] 获取失败:', err);
      wx.showToast({
        title: '获取订单失败',
        icon: 'error'
      });
    }
  },

  // 支付订单
  async payOrder(e) {
    try {
      const orderId = e.currentTarget.dataset.id;
      
      // 获取订单详情
      const orderRes = await db.collection('orders').doc(orderId).get();
      const order = orderRes.data;
      
      // 检查订单状态
      if (!order || order.status !== 0) {
        wx.showToast({
          title: order?.status > 0 ? '订单已支付' : '订单状态异常',
          icon: 'none'
        });
        return;
      }
      
      wx.showLoading({ title: '正在支付...' });
      
      // 调用支付功能
      const payParams = await wxpayService.createPayment({
        orderNo: order.orderNo,
        totalAmount: order.finalAmount || order.totalAmount,
        description: '支付订单'
      });
      
      // 发起支付
      await wxpayService.requestPayment(payParams);
      
      // 支付成功后更新订单状态
      await db.collection('orders').doc(orderId).update({
        data: {
          status: 1,
          statusText: '待发货',
          payTime: db.serverDate(),
          statusHistory: db.command.push({
            status: 1,
            statusText: '待发货',
            time: new Date(),
            desc: '支付成功'
          }),
          updateTime: db.serverDate()
        }
      });
      
      wx.hideLoading();
      wx.showToast({
        title: '支付成功',
        icon: 'success'
      });
      
      // 刷新订单列表
      this.getOrderList();
      
    } catch (err) {
      console.error('[支付] 支付失败:', err);
      wx.hideLoading();
      
      if (err.errMsg && err.errMsg.indexOf('cancel') > -1) {
        wx.showToast({
          title: '支付已取消',
          icon: 'none'
        });
      } else {
        wx.showToast({
          title: '支付失败',
          icon: 'none'
        });
      }
    }
  },

  // 确认收货
  confirmReceive(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.showModal({
      title: '确认收货',
      content: '确认已收到商品？',
      success: res => {
        if (res.confirm) {
          wx.cloud.callFunction({
            name: 'confirmOrder',
            data: {
              orderId: orderId,
              type: 'manual'
            }
          })
          .then(() => {
            wx.showToast({
              title: '确认成功',
              icon: 'success'
            });
            this.getOrderList();
          })
          .catch(err => {
            console.error('确认收货失败：', err);
            wx.showToast({
              title: '确认失败',
              icon: 'error'
            });
          });
        }
      }
    });
  },

  // 申请退款
  async applyRefund(e) {
    const orderId = e.currentTarget.dataset.id;
    const userInfo = wx.getStorageSync('userInfo');
    
    if (!userInfo || !userInfo.dbUserId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    const order = this.data.orderList.find(item => item._id === orderId);
    if (!order) {
      wx.showToast({
        title: '订单不存在',
        icon: 'error'
      });
      return;
    }

    // 验证订单所属
    if (order.userId !== userInfo.dbUserId) {
      wx.showToast({
        title: '无权操作此订单',
        icon: 'none'
      });
      return;
    }

    // 验证订单状态是否允许退款
    if (order.status !== 3) {
      wx.showToast({
        title: '当前状态不可退款',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '申请退款',
      content: '确认要对此订单申请退款吗？\n提交后客服会尽快处理您的申请。',
      success: async (res) => {
        if (!res.confirm) return;
        
        wx.showLoading({ title: '处理中...' });
        
        try {
          await db.collection('orders').doc(orderId).update({
            data: {
              status: 5,  // 退款中
              statusHistory: db.command.push({
                status: 5,
                time: db.serverDate(),
                desc: '用户申请退款',
                userId: userInfo.dbUserId
              }),
              refundInfo: {
                applyTime: db.serverDate(),
                userId: userInfo.dbUserId,
                status: 0,  // 0:待处理 1:已同意 2:已拒绝
                reason: '用户申请退款'
              },
              updateTime: db.serverDate()
            }
          });
          
          wx.hideLoading();
          wx.showToast({
            title: '申请已提交',
            icon: 'success'
          });
          
          // 刷新订单列表
          this.getOrderList();
          
        } catch (err) {
          console.error('[退款] 申请退款失败:', {
            错误: err.message,
            错误类型: err.name,
            错误堆栈: err.stack,
            订单ID: orderId,
            用户ID: userInfo.dbUserId,
            时间: new Date().toISOString()
          });
          
          wx.hideLoading();
          wx.showToast({
            title: '申请失败',
            icon: 'error'
          });
        }
      }
    });
  },

  // 跳转到订单详情
  goToOrderDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order/detail/index?id=${id}`
    });
  },

  onUnload() {
    // 页面卸载时清除所有定时器
    if (this.autoCheckInterval) {
      clearInterval(this.autoCheckInterval);
    }
    Object.values(this.data.countdownTimers).forEach(timer => {
      clearInterval(timer);
    });
  },

  // 需要刷新数据的情况（如取消订单、确认收货等）
  refreshList() {
    this.setData({
      allOrderData: {}
    });
    this.getOrderList();
  },

  // 订单项点击
  onOrderItemClick(e) {
    console.log('[点击] 订单项被点击:', {
      事件类型: e.type,
      点击位置: {
        x: e.detail.x,
        y: e.detail.y
      },
      订单ID: e.currentTarget.dataset.id
    });
    this.goToOrderDetail(e);
  },

  // 支付按钮点击
  onPayButtonClick(e) {
    console.log('[点击] 支付按钮被点击:', {
      事件类型: e.type,
      点击位置: {
        x: e.detail.x,
        y: e.detail.y
      },
      订单ID: e.currentTarget.dataset.id,
      订单信息: e.currentTarget.dataset.order
    });
    e.stopPropagation();  // 阻止事件冒泡
    this.payOrder(e);
  },

  // 取消按钮点击
  onCancelButtonClick(e) {
    console.log('[点击] 取消按钮被点击:', {
      事件类型: e.type,
      点击位置: {
        x: e.detail.x,
        y: e.detail.y
      },
      订单ID: e.currentTarget.dataset.id,
      订单信息: e.currentTarget.dataset.order
    });
    e.stopPropagation();  // 阻止事件冒泡
    this.cancelOrder(e);
  },

  // 开始倒计时
  startCountdown(orderId, timeLeft) {
    // 清除可能存在的旧定时器
    if (this.data.countdownTimers[orderId]) {
      clearInterval(this.data.countdownTimers[orderId]);
    }
    
    const timer = setInterval(() => {
      timeLeft -= 1000;
      
      if (timeLeft <= 0) {
        clearInterval(timer);
        // 自动取消订单
        this.autoCancelOrder(orderId);
        return;
      }
      
      // 更新显示的倒计时
      const minutes = Math.floor(timeLeft / 60000);
      const seconds = Math.floor((timeLeft % 60000) / 1000);
      
      // 更新对应订单的倒计时显示
      const orderList = this.data.orderList.map(order => {
        if (order._id === orderId) {
          return {
            ...order,
            countdown: `${minutes}:${seconds.toString().padStart(2, '0')}`
          };
        }
        return order;
      });
      
      this.setData({ orderList });
    }, 1000);
    
    // 保存定时器引用
    this.setData({
      ['countdownTimers.' + orderId]: timer
    });
  },

  // 自动取消订单
  async autoCancelOrder(orderId) {
    try {
      await db.collection('orders').doc(orderId).update({
        data: {
          status: 4,  // 已取消
          statusHistory: db.command.push({
            status: 4,
            time: db.serverDate(),
            desc: '超时自动取消'
          }),
          updateTime: db.serverDate()
        }
      });
      
      // 刷新订单列表
      this.getOrderList();
      
    } catch (err) {
      console.error('[订单] 自动取消订单失败:', err);
    }
  },

  async submitOrder() {
    try {
        wx.showLoading({ title: '提交订单中...' });

        // 获取订单信息
        const orderData = this.getOrderData(); // 假设有一个方法获取订单数据
        const totalFee = Math.floor(Number(orderData.totalAmount) * 100); // 转换为分

        // 检查金额
        if (totalFee <= 0) {
            throw new Error('订单金额必须大于0');
        }

        // 调用云函数提交订单并获取支付参数
        const result = await wx.cloud.callFunction({
            name: 'pay',
            data: {
                action: 'unifiedOrder',
                orderNo: orderData.orderNo,
                totalFee: totalFee
            }
        });

        const paymentParams = result.result.data; // 从云函数返回的支付参数
        if (paymentParams) {
            // 调用微信支付
            wx.requestPayment({
                ...paymentParams,
                success(res) {
                    console.log('支付成功', res);
                    // 更新订单状态等操作
                },
                fail(err) {
                    console.error('支付失败', err);
                }
            });
        } else {
            console.error('支付参数无效');
        }

    } catch (err) {
        console.error('[提交订单] 失败:', err);
        wx.hideLoading();
        wx.showToast({
            title: err.message || '提交订单失败',
            icon: 'none'
        });
    }
  }
}); 