/* pages/product/detail.wxss */ 
/* 整体容器样式 */
.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 商品主图样式 */
.product-images {
  width: 750rpx;
  height: 750rpx;
}

.product-images image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 商品基本信息区域样式 */
.info-section {
  background: #fff;
  padding: 24rpx 24rpx 32rpx;
  position: relative;
}

.title-section {
  padding: 30rpx;
  background: #fff;
  margin-top: 20rpx;
}

.product-name {
  font-size: 28rpx;
  line-height: 1.4;
  margin-right: 80rpx;
  margin-bottom: 16rpx;
}

.price-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-row {
  display: flex;
  align-items: baseline;
}

.label {
  font-size: 28rpx;
  color: #999;
  margin-right: 20rpx;
}

.price-section {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}

.final-price {
  font-size: 36rpx;
  color: #ff4d4f;
  font-weight: bold;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 12rpx;
}

.sales-info {
  font-size: 24rpx;
  color: #999;
}

.share-btn {
  position: absolute;
  right: 24rpx;
  top: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: none;
  border: none;
  padding: 0;
  font-size: 24rpx;
  color: #666;
  line-height: 1;
}

.share-btn::after {
  display: none;
}

.icon-share {
  font-size: 40rpx;
  color: #333;
}

/* 规格选择器 */
.spec-section {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #fff;
  margin-top: 2rpx;
}

.spec-label {
  font-size: 26rpx;
  color: #999;
  margin-right: 20rpx;
}

.spec-options {
  display: flex;
  gap: 16rpx;
}

.spec-option {
  padding: 6rpx 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 6rpx;
  font-size: 24rpx;
  color: #333;
  background: #fff;
  min-width: 72rpx;
  text-align: center;
}

.spec-option.active {
  background: #fff1f0;
  border-color: #ff4d4f;
  color: #ff4d4f;
}

/* 配送方式 */
.delivery-item {
  display: flex;
  align-items: center;
  padding: 28rpx 24rpx;
  background: #fff;
  margin-top: 2rpx;
}

.delivery-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 24rpx;
}

.delivery-value {
  flex: 1;
  font-size: 28rpx;
  color: #666;
}

/* 促销活动 */
.promotion-item {
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  background: #fff;
  margin: 2rpx 0;
  position: relative;
  z-index: 1;
  height: 45rpx;
  box-sizing: border-box;
  justify-content: flex-start;
}

.promotion-label {
  font-size: 28rpx;
  color: #ff4d4f;
  margin-right: 24rpx;
  min-width: 80rpx;
  position: relative;
  display: inline-block;
  line-height: 45rpx;
  vertical-align: middle;
}

.promotion-label::before {
  content: '';
  position: absolute;
  left: -8rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 4rpx;
  height: 24rpx;
  background: #ff4d4f;
  border-radius: 2rpx;
}

.promotion-content {
  flex: 1;
  display: inline-block;
  line-height: 45rpx;
  vertical-align: middle;
}

.promotion-value {
  font-size: 28rpx;
  color: #ff4d4f;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: inline-block;
  line-height: 45rpx;
  vertical-align: middle;
}

.promotion-value text {
  color: #ff4d4f;
  font-weight: bold;
  font-size: 32rpx;
  margin: 0 2rpx;
  display: inline-block;
  line-height: 45rpx;
  vertical-align: middle;
}

/* 详情板块 */
.specs-section,
.params-section,
.detail-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

/* 商品参数 */
.params-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.param-item {
  display: flex;
  justify-content: space-between;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.param-key {
  color: #999;
  font-size: 26rpx;
}

.param-value {
  color: #333;
  font-size: 26rpx;
  font-weight: 500;
}

/* 底部操作栏样式 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
  box-sizing: border-box;
}

.icon-btn {
  position: relative;
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  padding: 0;
  margin: 0;
  flex-shrink: 0;
}

.contact-btn {
  margin-right: 2rpx;
}

.cart-btn {
  position: relative;
  margin-right: auto;
}

.icon-btn image {
  width: 52rpx;
  height: 52rpx;
}

.badge {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  background: #ff4d4f;
  color: #fff;
  font-size: 20rpx;
  padding: 0 8rpx;
  min-width: 28rpx;
  height: 28rpx;
  border-radius: 14rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-cart, .buy-now {
  width: 150rpx !important;
  height: 72rpx !important;
  border-radius: 36rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  flex-shrink: 0;
}

.add-cart {
  background: #f5f5f5;
  color: #333;
  margin-right: 12rpx;
}

.buy-now {
  background: #987;
  color: #fff;
}

/* 移除按钮的默认边框 */
button.icon-btn::after,
.add-cart::after,
.buy-now::after {
  display: none;
}

/* 详情部分 */
.detail-header {
  width: 100%;
  text-align: center;
  margin-bottom: 30rpx;
}

.main-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.sub-title {
  font-size: 28rpx;
  color: #666;
}

.feature-list {
  padding: 0 30rpx;
}

.feature-item {
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
}

.feature-num {
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  border-radius: 50%;
  background: #666;
  color: #fff;
  margin-right: 20rpx;
}

.feature-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.feature-desc {
  font-size: 28rpx;
  color: #666;
}

/* 详情图片样式 */
.detail-images {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 0;
  line-height: 0;
}

.detail-image {
  width: 100%;
  vertical-align: top;
  margin: 0;
  display: block;
}

.no-detail-image {
  text-align: center;
  color: #999;
  padding: 30rpx 0;
}

/* 详情区域样式 */
.detail-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 20rpx 0;
  background: #fff;
}

/* 覆盖微信小程序 contact 按钮的默认样式 */
button.icon-btn[open-type="contact"] {
  width: 88rpx !important;
  height: 88rpx !important;
  margin: 0 !important;
  padding: 0 !important;
  line-height: 1 !important;
  background: none !important;
  border: none !important;
  border-radius: 0 !important;
}

button.icon-btn[open-type="contact"]::after {
  display: none;
  border: none;
}

/* 确保图标居中显示 */
button.icon-btn[open-type="contact"] image {
  margin: 0 auto;
  display: block;
}

/* 箭头样式 */
.arrow {
  width: 12rpx;
  height: 12rpx;
  border-top: 2rpx solid #999;
  border-right: 2rpx solid #999;
  transform: rotate(45deg);
}