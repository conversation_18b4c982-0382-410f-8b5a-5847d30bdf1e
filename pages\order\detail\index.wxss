.container {
  min-height: 100vh;
  background: #f8f8f8;
  padding-bottom: 40rpx;
}

.status-section {
  background: #987;
  padding: 40rpx 30rpx;
}

.status-text {
  color: #fff;
  font-size: 32rpx;
}

.address-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.contact {
  margin-bottom: 10rpx;
}

.name {
  font-size: 28rpx;
  margin-right: 20rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.address {
  font-size: 26rpx;
  color: #333;
}

.products-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.product-item {
  display: flex;
  margin-bottom: 30rpx;
}

.product-item:last-child {
  margin-bottom: 0;
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.product-spec {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.price-quantity {
  display: flex;
  justify-content: space-between;
}

.price {
  color: #987;
  font-size: 28rpx;
}

.quantity {
  color: #999;
  font-size: 24rpx;
}

.order-info {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 26rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
}

.value {
  color: #333;
}

.price-section {
  background: #fff;
  padding: 30rpx;
}

.price-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 26rpx;
}

.price-item.discount {
  color: #666;
}

.discount-value {
  color: #ff4d4f;
}

.price-item.final {
  margin-top: 20rpx;
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.final-price {
  color: #ff4d4f;
  font-weight: bold;
}

.total {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
  font-size: 28rpx;
  font-weight: 500;
}

.status-bar {
  background: #987;
  color: #fff;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.status-text {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.status-desc {
  font-size: 26rpx;
  opacity: 0.9;
} 