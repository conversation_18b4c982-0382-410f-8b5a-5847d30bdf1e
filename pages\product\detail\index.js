const db = wx.cloud.database();
const app = getApp();
import paymentService from '../../../utils/paymentService';

Page({
  data: {
    product: null,
    selectedSpec: '42ml', // 默认选中42ml
    quantity: 1,
    promotions: [],
    finalPrice: 0,
    isScrollUp: true,
    lastScrollTop: 0,
    discountDetails: [],
    currentPrice: null,
    specs: [
      { size: '42ml', price: 248 },
      { size: '84ml', price: 398 }
    ],
    isLogin: false,
    userInfo: null,
    isLoading: false  // 添加加载状态标记
  },

  onLoad(options) {
    if (options.id) {
      // 防止重复加载
      if (this.data.isLoading) {
        console.log('[商品详情] 正在加载中，跳过重复请求');
        return;
      }

      // 检查缓存
      const cached = app.globalData.productCache.get(options.id);
      if (cached) {
        console.log('[商品详情] 使用缓存数据');
        this.setData({
          product: cached.productData,
          currentPrice: cached.currentPrice,
          finalPrice: cached.finalPrice,
          discountDetails: cached.discountDetails,
          promotions: cached.promotions
        });
      } else {
        this.getProductDetail(options.id);
      }
    }

    // 获取用户信息
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.isLogin) {
      this.setData({
        isLogin: true,
        userInfo: userInfo
      });
    }

    // 监听登录状态变化
    app.globalData.eventBus.on('loginStateChanged', this.handleLoginStateChanged);
  },

  onHide() {
    // 页面隐藏时重置加载状态
    this.setData({ isLoading: false });
  },

  onUnload() {
    // 取消监听
    app.globalData.eventBus.off('loginStateChanged', this.handleLoginStateChanged);
  },

  // 处理登录状态变化
  handleLoginStateChanged(data) {
    this.setData({
      isLogin: data.isLogin,
      userInfo: data.userInfo
    });
  },

  // 获取商品详情
  async getProductDetail(id) {
    try {
      // 设置加载状态
      this.setData({ isLoading: true });
      wx.showLoading({ title: '加载中' });
      
      // 添加错误处理和重试逻辑
      let retryCount = 0;
      const maxRetries = 3;
      let res;

      while (retryCount < maxRetries) {
        try {
          res = await db.collection('products').doc(id).get();
          break;
        } catch (err) {
          retryCount++;
          if (retryCount === maxRetries) {
            throw err;
          }
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      if (!res || !res.data) {
        throw new Error('商品数据获取失败');
      }
      
      // 设置初始规格价格
      const initialSpec = this.data.specs.find(s => s.size === this.data.selectedSpec);
      
      // 初始化商品数据并一次性计算价格
      const finalPriceResult = await this.calculateFinalPrice(initialSpec.price);
      
      // 确保商品数据完整性
      const productData = {
        ...res.data,
        images: res.data.images || [],
        description: res.data.description || '暂无描述'
      };

      // 获取促销信息
      const promotionRes = await this.getPromotions();

      // 更新缓存
      app.globalData.productCache.set(id, {
        productData,
        currentPrice: initialSpec.price,
        finalPrice: finalPriceResult.finalPrice,
        discountDetails: finalPriceResult.discountDetails,
        promotions: promotionRes
      });

      this.setData({
        product: productData,
        currentPrice: initialSpec.price,
        finalPrice: finalPriceResult.finalPrice,
        discountDetails: finalPriceResult.discountDetails,
        promotions: promotionRes,
        isLoading: false  // 重置加载状态
      });

      wx.hideLoading();
    } catch (err) {
      console.error('[商品详情] 加载失败:', {
        错误: err.message,
        错误类型: err.name,
        错误堆栈: err.stack,
        商品ID: id,
        时间: new Date().toISOString()
      });
      
      this.setData({ isLoading: false });  // 重置加载状态
      wx.hideLoading();
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 获取促销信息
  async getPromotions() {
    try {
      if (!this.data.product || !this.data.product._id) {
        console.warn('[促销] 商品数据不完整，跳过促销查询');
        return [];
      }

      const promotionRes = await db.collection('promotions')
        .where({
          status: 1,
          startTime: db.command.lte(db.serverDate()),
          endTime: db.command.gte(db.serverDate())
        })
        .get();

      return promotionRes.data;

    } catch (err) {
      console.error('[促销] 获取促销信息失败:', {
        错误: err.message,
        商品ID: this.data.product?._id,
        时间: new Date().toISOString()
      });
      return [];
    }
  },

  // 选择规格
  async selectSpec(e) {
    const spec = e.currentTarget.dataset.spec;
    const specInfo = this.data.specs.find(s => s.size === spec);
    
    if (!specInfo) return;

    // 一次性更新所有价格相关数据
    const finalPriceResult = await this.calculateFinalPrice(specInfo.price);
    this.setData({
      selectedSpec: spec,
      currentPrice: specInfo.price,
      finalPrice: finalPriceResult.finalPrice,
      discountDetails: finalPriceResult.discountDetails
    });
  },

  // 更改数量
  changeQuantity(e) {
    const type = e.currentTarget.dataset.type;
    let quantity = this.data.quantity;

    if (type === 'minus' && quantity > 1) {
      quantity--;
    } else if (type === 'plus' && quantity < 99) {
      quantity++;
    }

    // 一次性更新数量和价格
    this.setData({ quantity });
    this.calculateFinalPrice(this.data.currentPrice);
  },

  // 计算最终价格
  async calculateFinalPrice(basePrice) {
    try {
      const promotionResult = await promotionService.calculatePromotionPrice(
        basePrice,
        this.data.product._id
      );

      return promotionResult;
    } catch (err) {
      console.error('[商品] 计算价格失败:', err);
      return {
        finalPrice: basePrice,
        discountDetails: []
      };
    }
  },

  // 添加到购物车
  async addToCart() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({ title: '添加中' });
      
      // 构建购物车数据
      const cartData = {
        product: {
          _id: this.data.product._id,
          name: this.data.product.name,
          image: this.data.product.images[0]
        },
        specs: {
          name: this.data.selectedSpec,
          price: this.data.currentPrice
        },
        quantity: this.data.quantity,
        selected: false,
        isDeleted: false,
        createTime: db.serverDate()
      };

      await db.collection('cart').add({ data: cartData });

      wx.hideLoading();
      wx.showToast({
        title: '添加成功',
        icon: 'success'
      });

    } catch (err) {
      console.error('[购物车] 添加失败:', {
        错误: err.message,
        错误类型: err.name,
        错误堆栈: err.stack,
        商品ID: this.data.product._id,
        时间: new Date().toISOString()
      });
      wx.hideLoading();
      wx.showToast({
        title: '添加失败',
        icon: 'error'
      });
    }
  },

  // 立即购买
  async buyNow() {
    try {
      if (!this.data.isLogin) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      wx.showLoading({ title: '处理中' });

      // 创建订单
      const orderData = {
        orderNo: `UW${Date.now()}${Math.random().toString(36).substr(2, 4)}`,
        product: {
          _id: this.data.product._id,
          name: this.data.product.name,
          image: this.data.product.images[0]
        },
        specs: {
          name: this.data.selectedSpec,
          price: this.data.currentPrice
        },
        quantity: this.data.quantity,
        totalFee: this.data.finalPrice * 100, // 转换为分
        status: 0,  // 0: 待支付
        createTime: db.serverDate()
      };

      const db = wx.cloud.database();
      const orderRes = await db.collection('orders').add({
        data: orderData
      });

      // 发起支付
      await paymentService.requestPayment(
        orderRes._id,
        orderData.totalFee
      );

      wx.hideLoading();
      wx.showToast({
        title: '支付成功',
        icon: 'success'
      });

      // 跳转到订单列表
      wx.redirectTo({
        url: '/pages/order/list'
      });

    } catch (err) {
      console.error('[购买] 处理失败:', err);
      wx.hideLoading();
      wx.showToast({
        title: err.message || '购买失败',
        icon: 'none'
      });
    }
  },

  // 页面滚动处理
  onPageScroll(e) {
    const scrollTop = e.scrollTop;
    const isScrollUp = scrollTop < this.data.lastScrollTop;
    
    this.setData({
      isScrollUp,
      lastScrollTop: scrollTop
    });
  }
}); 