<view class="container">
  <view class="info-list">
    <view class="info-item" bindtap="changeAvatar">
      <text class="label">头像</text>
      <view class="right-content">
        <view class="avatar">
          <image src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
        </view>
        <text class="arrow">></text>
      </view>
    </view>
    <view class="info-item" bindtap="changeNickname">
      <text class="label">昵称</text>
      <view class="right-content">
        <text class="value">{{userInfo.nickName}}</text>
        <text class="arrow">></text>
      </view>
    </view>
    <view class="info-item">
      <text class="label">用户ID</text>
      <text class="value user-id">{{userInfo.userId}}</text>
    </view>
    <view class="info-item">
      <text class="label">手机号</text>
      <text class="value">{{userInfo.phone || '未绑定'}}</text>
    </view>
    <view class="info-item" bindtap="changeGender">
      <text class="label">性别</text>
      <view class="right-content">
        <text class="value">{{userInfo.gender === 1 ? '男' : '女'}}</text>
        <text class="arrow">></text>
      </view>
    </view>
    <view class="info-item" bindtap="changeAge">
      <text class="label">年龄</text>
      <view class="right-content">
        <text class="value">{{userInfo.age || '未设置'}}</text>
        <text class="arrow">></text>
      </view>
    </view>
  </view>
</view> 