// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 引入云支付模块
const {
  Payment: CloudPayment, 
  getPayParams, 
  verifyPaymentNotify
} = cloud.cloudPay;

// 数据库
const db = cloud.database();

// 支付配置
const payConfig = {
  // 小程序appid，从环境变量或者固定配置获取
  appid: process.env.APPID || 'wxf57755081128c53d',
  // 商户号，从环境变量或者固定配置获取
  mchid: process.env.MCHID || '1699893814',
  // 支付回调地址，更新为新的回调处理云函数
  notifyUrl: process.env.NOTIFY_URL || 'https://cloud1-2gfror0a1b871162.service.tcloudbase.com/payCallbackHandler',
  // 环境ID，可用于区分沙箱环境和生产环境
  envId: cloud.DYNAMIC_CURRENT_ENV
};

// 云函数入口
exports.main = async (event, context) => {
  console.log('[微信支付云函数] 收到请求:', {
    action: event.action,
    userInfo: context.userInfo, 
    params: { ...event, env: cloud.DYNAMIC_CURRENT_ENV }
  });

  // 对于处理支付回调的请求，不需要校验openid
  if (event.action === 'processNotify') {
    return await processNotify(event);
  }

  // 获取用户openid
  const openId = event.openid || (context.userInfo ? context.userInfo.openId : null);
  if (!openId) {
    return {
      code: -1,
      message: '缺少用户标识，请检查登录状态'
    };
  }

  try {
    // 根据action执行不同操作
    switch (event.action) {
      case 'unifiedOrder': // 统一下单
        return await unifiedOrder(event, openId);
      case 'queryOrder': // 查询订单
        return await queryOrder(event, openId);
      case 'closeOrder': // 关闭订单
        return await closeOrder(event, openId);
      case 'refund': // 申请退款
        return await createRefund(event, openId);
      case 'queryRefund': // 查询退款
        return await queryRefund(event, openId);
      case 'mockPayment': // 模拟支付
        return await mockPayment(event, openId);
      default:
        return {
          code: -1,
          message: '未知的操作类型'
        };
    }
  } catch (err) {
    console.error('[微信支付云函数] 处理失败:', err);
    return {
      code: -1,
      message: err.message || '支付处理失败',
      error: err.stack || err
    };
  }
}

/**
 * 统一下单
 * @param {Object} event 请求参数
 * @param {String} openId 用户OpenID
 */
async function unifiedOrder(event, openId) {
  // 参数验证
  if (!event.orderNo) {
    return { code: -1, message: '缺少订单号' };
  }
  
  if (!event.totalFee || event.totalFee <= 0) {
    return { code: -1, message: '无效的支付金额' };
  }

  const orderNo = event.orderNo;
  const totalFee = parseInt(event.totalFee);
  
  // 获取环境信息
  const accountInfo = cloud.getWXContext().header && 
                      cloud.getWXContext().header['x-wx-env'] || {};
  const env = accountInfo.envVersion || 'release';
  
  // 根据参数和环境判断是否使用模拟支付
  // 1. 如果参数明确要求模拟支付，则使用模拟支付
  // 2. 如果不是生产环境且未强制要求真实支付，使用模拟支付
  const useMockPayment = event.mockPayment === true || 
                        (env !== 'release' && !event.forceRealPayment);
  
  console.log('[统一下单] 环境和支付方式:', {
    环境: env,
    使用模拟支付: useMockPayment ? '是' : '否',
    强制真实支付: event.forceRealPayment ? '是' : '否'
  });

  // 检查订单是否已存在
  try {
    const orderCheck = await db.collection('orders').where({
      orderNo: orderNo
    }).get();
    
    if (orderCheck.data.length === 0) {
      return { code: -1, message: '订单不存在' };
    }
    
    // 检查订单状态
    const order = orderCheck.data[0];
    if (order.status !== 0 && order.status !== '待支付') {
      return { code: -1, message: '订单状态不允许支付' };
    }
  } catch (err) {
    console.error('[统一下单] 检查订单失败:', err);
    // 继续处理，不要因为查询失败而阻止支付
  }

  // 如果是模拟支付，直接返回模拟的支付参数
  if (useMockPayment) {
    console.log('[统一下单] 使用模拟支付');
    return await mockPayment(event, openId);
  }

  try {
    console.log('[统一下单] 开始真实支付流程');
    // 创建支付实例
    const payment = new CloudPayment({
      appid: payConfig.appid,
      mchid: payConfig.mchid,
      envId: payConfig.envId
    });

    // 构造订单信息
    const params = {
      // 商品描述，例如"智慧商城-商品购买"
      description: `UW小程序-订单${orderNo}`,
      // 商户订单号
      out_trade_no: orderNo,
      // 附加数据，可以用来标识用途等
      attach: 'UW小程序',
      // 订单金额信息
      amount: {
        // 总金额，单位为分
        total: totalFee,
        // 货币类型，CNY：人民币
        currency: 'CNY'
      },
      // 支付者信息
      payer: {
        openid: openId
      },
      // 回调通知地址
      notify_url: payConfig.notifyUrl
    };

    // 调用统一下单API
    const result = await payment.unifiedOrder({
      openid: openId,
      body: '小程序订单',
      out_trade_no: orderNo,
      total_fee: totalFee,
      trade_type: 'JSAPI',
      spbill_create_ip: '127.0.0.1',
      notify_url: payConfig.notifyUrl,
      success_url: '',
      fail_url: ''
    });

    if (result.returnCode !== 'SUCCESS' || result.resultCode !== 'SUCCESS') {
      console.error('[统一下单] 请求失败:', result);
      return {
        code: -1,
        message: result.errCodeDes || result.returnMsg || '下单失败'
      };
    }

    // 获取小程序支付需要的参数
    const payParams = getPayParams({
      ...result,
      appid: payConfig.appid, // 必需
      timeStamp: Math.floor(Date.now() / 1000).toString(), // 当前时间戳，必需
    });

    console.log('[统一下单] 生成支付参数:', {
      ...payParams,
      paySign: '(已隐藏)'
    });

    // 添加total_fee字段，保持和现有实现一致
    payParams.total_fee = totalFee;

    return {
      code: 0,
      message: '下单成功',
      data: payParams
    };
  } catch (err) {
    console.error('[统一下单] 真实支付失败，错误：', err);
    return {
      code: -1,
      message: '下单失败：' + (err.message || err.errMsg || '未知错误')
    };
  }
}

/**
 * 模拟支付（开发环境使用）
 */
async function mockPayment(event, openId) {
  console.log('[模拟支付] 参数:', {
    订单号: event.orderNo,
    金额: event.totalFee,
    openid: openId
  });
  
  // 保存total_fee，确保模拟支付也可以获取
  const totalFee = parseInt(event.totalFee || 1);
  
  // 生成模拟的支付参数
  const timestamp = '' + Math.floor(Date.now() / 1000);
  const nonceStr = Math.random().toString(36).substr(2, 15);
  
  // 构造模拟支付参数
  const mockPayParams = {
    timeStamp: timestamp,
    nonceStr: nonceStr,
    package: `prepay_id=mock_prepay_id_${timestamp}`,
    signType: 'MD5',
    paySign: `MOCK_SIGNATURE_${timestamp}_${nonceStr}`,
    _mock: true,  // 标记为模拟支付
    total_fee: totalFee  // 添加total_fee参数
  };
  
  console.log('[模拟支付] 返回模拟支付参数');
  
  return {
    code: 0,
    message: '获取支付参数成功（模拟支付）',
    data: mockPayParams
  };
}

/**
 * 查询订单
 * @param {Object} event 请求参数
 * @param {String} openId 用户OpenID
 */
async function queryOrder(event, openId) {
  // 参数验证
  if (!event.orderNo && !event.transactionId) {
    return { code: -1, message: '缺少订单号或交易号' };
  }
  
  const orderNo = event.orderNo;
  const transactionId = event.transactionId;
  
  try {
    console.log('[查询订单] 参数:', {
      商户订单号: orderNo,
      微信交易号: transactionId
    });
    
    // 创建支付实例
    const payment = new CloudPayment({
      appid: payConfig.appid,
      mchid: payConfig.mchid,
      envId: payConfig.envId
    });
    
    // 查询订单
    const result = await payment.orderQuery({
      out_trade_no: orderNo
    });
    
    console.log('[查询订单] 查询结果:', result);
    
    return {
      code: 0,
      message: '查询成功',
      data: result
    };
  } catch (err) {
    console.error('[查询订单] 查询失败:', err);
    return {
      code: -1,
      message: '查询订单失败: ' + (err.message || err.errMsg || '未知错误')
    };
  }
}

/**
 * 关闭订单
 * @param {Object} event 请求参数
 * @param {String} openId 用户OpenID
 */
async function closeOrder(event, openId) {
  // 参数验证
  if (!event.orderNo) {
    return { code: -1, message: '缺少订单号' };
  }
  
  const orderNo = event.orderNo;
  
  try {
    console.log('[关闭订单] 参数:', {
      商户订单号: orderNo
    });
    
    // 创建支付实例
    const payment = new CloudPayment({
      appid: payConfig.appid,
      mchid: payConfig.mchid,
      envId: payConfig.envId
    });
    
    // 关闭订单
    const result = await payment.closeOrder({
      out_trade_no: orderNo
    });
    
    console.log('[关闭订单] 结果:', result);
    
    return {
      code: 0,
      message: '关闭成功',
      data: result
    };
  } catch (err) {
    console.error('[关闭订单] 关闭失败:', err);
    return {
      code: -1,
      message: '关闭订单失败: ' + (err.message || err.errMsg || '未知错误')
    };
  }
}

/**
 * 申请退款
 * @param {Object} event 请求参数
 * @param {String} openId 用户OpenID
 */
async function createRefund(event, openId) {
  // 参数验证
  if (!event.refundParams || !event.refundParams.out_trade_no) {
    return { code: -1, message: '缺少退款参数' };
  }
  
  const refundParams = event.refundParams;
  
  try {
    console.log('[申请退款] 参数:', refundParams);
    
    // 创建支付实例
    const payment = new CloudPayment({
      appid: payConfig.appid,
      mchid: payConfig.mchid,
      envId: payConfig.envId
    });
    
    // 申请退款
    const result = await payment.refund({
      out_trade_no: refundParams.out_trade_no,
      out_refund_no: refundParams.out_refund_no || `REFUND_${Date.now()}`,
      total_fee: refundParams.total_fee,
      refund_fee: refundParams.refund_fee,
      refund_desc: refundParams.refund_desc || '用户申请退款'
    });
    
    console.log('[申请退款] 结果:', result);
    
    return {
      code: 0,
      message: '退款申请成功',
      data: result
    };
  } catch (err) {
    console.error('[申请退款] 失败:', err);
    return {
      code: -1,
      message: '申请退款失败: ' + (err.message || err.errMsg || '未知错误')
    };
  }
}

/**
 * 查询退款
 * @param {Object} event 请求参数
 * @param {String} openId 用户OpenID
 */
async function queryRefund(event, openId) {
  // 参数验证
  if (!event.refundId) {
    return { code: -1, message: '缺少退款订单号' };
  }
  
  const refundId = event.refundId;
  
  try {
    console.log('[查询退款] 参数:', {
      退款单号: refundId
    });
    
    // 创建支付实例
    const payment = new CloudPayment({
      appid: payConfig.appid,
      mchid: payConfig.mchid,
      envId: payConfig.envId
    });
    
    // 查询退款
    const result = await payment.refundQuery({
      out_refund_no: refundId
    });
    
    console.log('[查询退款] 结果:', result);
    
    return {
      code: 0,
      message: '查询成功',
      data: result
    };
  } catch (err) {
    console.error('[查询退款] 失败:', err);
    return {
      code: -1,
      message: '查询退款失败: ' + (err.message || err.errMsg || '未知错误')
    };
  }
}

/**
 * 处理支付回调通知
 * @param {Object} event 请求参数
 */
async function processNotify(event) {
  try {
    console.log('[支付回调] 收到数据:', event);
    
    // 判断是否是测试环境直接调用
    const isTestCall = !event.headers && !event.httpMethod;
    
    if (isTestCall) {
      console.log('[支付回调] 检测到测试环境直接调用，使用模拟数据');
      
      // 在测试环境中返回成功
      return {
        code: 0,
        message: '测试环境模拟支付回调成功',
        data: {
          test: true,
          success: true
        }
      };
    }
    
    // 真实回调处理逻辑
    // 验证回调通知
    let result;
    try {
      result = verifyPaymentNotify(event);
    } catch (err) {
      console.error('[支付回调] 验证通知失败:', err);
      return {
        code: -1,
        message: '验证通知失败: ' + err.message
      };
    }
    
    // 验证通知的数据
    const { resource } = result;
    
    if (!resource || !resource.ciphertext) {
      console.error('[支付回调] 回调数据格式错误', { 
        事件数据: event,
        解析结果: result
      });
      return {
        code: -1,
        message: '回调数据格式错误'
      };
    }
    
    // 验证成功，处理订单状态
    const { out_trade_no, transaction_id, trade_state, amount } = resource.json;
    
    console.log('[支付回调] 解析结果:', {
      订单号: out_trade_no,
      交易号: transaction_id,
      交易状态: trade_state,
      金额: amount
    });
    
    // 更新订单状态
    if (trade_state === 'SUCCESS') {
      try {
        // 查询数据库中的订单
        const orderRes = await db.collection('orders').where({
          orderNo: out_trade_no
        }).get();
        
        if (orderRes.data.length === 0) {
          console.error('[支付回调] 订单不存在:', out_trade_no);
          return {
            code: -1,
            message: '订单不存在'
          };
        }
        
        const order = orderRes.data[0];
        
        // 更新订单状态
        await db.collection('orders').doc(order._id).update({
          data: {
            status: 1, // 已支付
            payTime: new Date(),
            transactionId: transaction_id,
            updateTime: db.serverDate()
          }
        });
        
        console.log('[支付回调] 订单状态更新成功:', out_trade_no);
        
        return {
          code: 0,
          message: 'SUCCESS'
        };
      } catch (err) {
        console.error('[支付回调] 更新订单状态失败:', err);
        return {
          code: -1,
          message: '更新订单状态失败: ' + err.message
        };
      }
    } else {
      console.log('[支付回调] 交易未成功:', trade_state);
      return {
        code: 0,
        message: '交易未成功，状态：' + trade_state
      };
    }
  } catch (err) {
    console.error('[支付回调] 处理失败:', err);
    return {
      code: -1,
      message: '处理支付回调失败: ' + (err.message || err.errMsg || '未知错误')
    };
  }
} 