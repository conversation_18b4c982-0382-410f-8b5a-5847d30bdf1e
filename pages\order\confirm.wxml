<!-- <navigation-bar title="订单确认" show-back="{{true}}"></navigation-bar> -->

<!-- 主内容区域 -->
<view class="container">
  <!-- 收货地址 -->
  <view class="address-section" bindtap="selectAddress">
    <block wx:if="{{address}}">
      <view class="address-info">
        <view class="contact">
          <text class="name">{{address.name}}</text>
          <text class="phone">{{address.phone}}</text>
        </view>
        <view class="address">{{address.province}}{{address.city}}{{address.district}}{{address.detail}}</view>
      </view>
    </block>
    <block wx:else>
      <view class="no-address">
        <text>添加收货地址</text>
      </view>
    </block>
    <view class="arrow">
      <text>></text>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="product-list">
    <block wx:for="{{orderInfo.products}}" wx:key="index">
      <view class="product-item">
        <image class="product-image" src="{{item.image}}" mode="aspectFill" binderror="onImageError" data-index="{{index}}"></image>
        <view class="product-info">
          <view class="product-name">{{item.name}}</view>
          <view class="product-spec" wx:if="{{item.specs}}">{{item.specs.name}}</view>
          <view class="price-quantity">
            <text class="price">{{item.price}}</text>
            <text class="quantity">x{{item.quantity}}</text>
          </view>
        </view>
      </view>
    </block>
  </view>
  
  <!-- 订单金额 -->
  <view class="order-amount">
    <view class="amount-item">
      <text>商品金额</text>
      <text>{{totalAmount}}</text>
    </view>
    
    <!-- 促销活动优惠（强制应用） -->
    <view class="amount-item" wx:if="{{promotionInfo}}">
      <text>促销活动</text>
      <text class="discount-value">{{promotionInfo.name}} -{{promotionDiscount}}</text>
    </view>
    
    <!-- 优惠券或会员折扣（用户可选） -->
    <view class="amount-item coupon-select" bindtap="chooseDiscount">
      <text>优惠券/会员</text>
      <view class="coupon-info">
        <block wx:if="{{selectedDiscount}}">
          <text class="discount-value">{{selectedDiscount.name}} -{{userDiscountAmount}}</text>
        </block>
        <block wx:else>
          <text class="no-discount">{{discounts.length > 0 ? '点击选择优惠券' : '暂无可用优惠券'}}</text>
        </block>
        <text class="arrow">></text>
      </view>
    </view>
    
    <view class="amount-item">
      <text>运费</text>
      <text>包邮</text>
    </view>
    <view class="amount-item total">
      <text>实付款</text>
      <text class="final-price">{{finalPrice || totalAmount}}</text>
    </view>
  </view>
  
  <!-- 备注 -->
  <view class="remark-section">
    <view class="label">备注</view>
    <input class="remark-input" placeholder="可以告诉我们您的特殊需求" model:value="{{remark}}"></input>
  </view>
  
  <!-- 底部提交区域 -->
  <view class="bottom-bar">
    <view class="total-section">
      <text>合计：</text>
      <text class="final-price">{{finalPrice || totalAmount}}</text>
    </view>
    <button class="submit-btn" bindtap="submitOrder" disabled="{{!address}}" style="position: relative; left: -55rpx; top: -1rpx">提交订单</button>
  </view>
</view>

<!-- 安全区域 -->
<view style="height: 34px;"></view>

<!-- 自定义底部栏 -->
<!-- <custom-tab-bar></custom-tab-bar> --> 