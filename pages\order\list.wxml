<view class="container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <view class="nav-item {{currentTab === 0 ? 'active' : ''}}" 
          bindtap="switchTab" data-index="0">
      <text class="nav-text">待付款</text>
      <view class="nav-line" wx:if="{{currentTab === 0}}"></view>
    </view>
    <view class="nav-item {{currentTab === 1 ? 'active' : ''}}" 
          bindtap="switchTab" data-index="1">
      <text class="nav-text">待发货</text>
      <view class="nav-line" wx:if="{{currentTab === 1}}"></view>
    </view>
    <view class="nav-item {{currentTab === 2 ? 'active' : ''}}" 
          bindtap="switchTab" data-index="2">
      <text class="nav-text">已发货</text>
      <view class="nav-line" wx:if="{{currentTab === 2}}"></view>
    </view>
    <view class="nav-item {{currentTab === 3 ? 'active' : ''}}" 
          bindtap="switchTab" data-index="3">
      <text class="nav-text">退款/售后</text>
      <view class="nav-line" wx:if="{{currentTab === 3}}"></view>
    </view>
  </view>

  <!-- 订单列表 -->
  <scroll-view scroll-y class="order-list">
    <view class="order-item" 
          wx:for="{{orderList}}" 
          wx:key="_id" 
          bindtap="goToOrderDetail"
          data-id="{{item._id}}">
      <view class="order-header">
        <text class="order-no">订单号：{{item.orderNo}}</text>
        <text class="order-status">{{statusText[item.status]}}</text>
      </view>
      <!-- 待付款订单的倒计时 -->
      <view class="countdown-bar" wx:if="{{item.status === 0 && item.countdown}}">
        <text class="countdown-text">请在</text>
        <text class="countdown-time">{{item.countdown}}</text>
        <text class="countdown-text">内完成支付，超时订单将自动取消</text>
      </view>
      <view class="order-content">
        <view wx:for="{{item.products}}" wx:key="_id" wx:for-item="product" class="product-item">
          <image class="product-image" src="{{product.mainImage || product.image}}" mode="aspectFill"/>
          <view class="product-info">
            <view class="product-name">{{product.name}}</view>
            <view class="product-spec" wx:if="{{product.specs}}">{{product.specs.name}}</view>
            <view class="price-quantity">
              <text class="price">¥{{product.price}}</text>
              <text class="quantity">x{{product.quantity}}</text>
            </view>
          </view>
        </view>
      </view>
      <view class="order-footer">
        <text class="total">合计：¥{{item.totalAmount}}</text>
        <view class="order-actions">
          <button 
            wx:if="{{item.status === 0}}" 
            class="action-btn cancel-btn"
            data-id="{{item._id}}"
            data-order="{{item}}"
            catch:tap="cancelOrder"
          >取消订单</button>
          <button 
            wx:if="{{item.status === 0}}" 
            class="action-btn pay-btn"
            data-id="{{item._id}}"
            catch:tap="payOrder"
          >立即付款</button>
          <button 
            wx:if="{{item.status === 2}}" 
            class="action-btn confirm-btn"
            data-id="{{item._id}}"
            catch:tap="confirmReceive"
          >确认收货</button>
          <button 
            wx:if="{{item.status === 3}}" 
            class="action-btn refund-btn"
            catchtap="applyRefund" 
            data-id="{{item._id}}"
          >退款</button>
        </view>
      </view>
    </view>
    <!-- 空状态 -->
    <view class="empty" wx:if="{{orderList.length === 0}}" style="height: 192rpx; display: block; box-sizing: border-box; position: relative; left: 0rpx; top: 27rpx">
      <text>暂无相关订单</text>
    </view>
  </scroll-view>
</view> 