.product-section {
  background: #fff;
  margin: 20rpx;
  padding: 20rpx;
  border-radius: 12rpx;
}

.product-item {
  display: flex;
  padding: 10rpx;
  height: 100rpx;
  border-bottom: 1rpx solid #f5f5f5;
  align-items: center;
}

.product-thumb {
  width: 100rpx;
  height: 100rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
}

.left-info {
  flex: 1;
  overflow: hidden;
  margin-right: 20rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-spec {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}

.right-info {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.price {
  color: #ff4d4f;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.quantity {
  color: #666;
  font-size: 24rpx;
  min-width: 50rpx;
  text-align: right;
} 