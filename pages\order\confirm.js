// pages/order/confirm.js
import wxpayService from '../../utils/wxpayService';
import promotionService from '../../services/promotion';
const db = wx.cloud.database()
// 去掉所有导入，避免因为缺少某些服务而导致页面加载失败

Page({

  /**
   * 页面的初始数据
   */
  data: {
    orderInfo: null,
    address: null,
    totalAmount: '0.00',
    finalPrice: 0,
    remark: '',  // 添加备注字段
    submitting: false,  // 控制提交按钮状态
    // 添加优惠相关字段
    discounts: [],  // 可用的优惠列表
    selectedDiscount: null,  // 选中的优惠
    discountAmount: 0,  // 优惠金额
    isMember: false,  // 是否是会员
    memberDiscount: 0  // 会员折扣金额
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('订单确认页面加载，参数:', options);
    
    try {
      // 验证用户登录状态
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo || !userInfo._openid) {
        this.showError('请先登录');
        return;
      }
      
      // 获取上一页传递的商品信息
      const eventChannel = this.getOpenerEventChannel();
      
      if (!eventChannel) {
        this.showError('页面加载异常');
        return;
      }
      
      eventChannel.on('acceptDataFromOpenerPage', data => {
        if (!data || !data.products || !data.products.length) {
          this.showError('缺少商品信息');
          return;
        }
        
        // 计算总金额
        const totalAmount = this.calculateTotal(data.products);
        
        this.setData({
          orderInfo: {
            products: data.products
          },
          totalAmount,
          finalPrice: totalAmount,
          userInfo: userInfo
        });
        
        // 加载用户优惠信息
        this.loadUserDiscounts();
      });
    } catch (err) {
      console.error('订单确认页面加载错误:', err);
      this.showError('页面加载失败');
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('订单确认页面显示');
    // 获取默认地址
    this.getDefaultAddress();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 计算总金额
  calculateTotal(products) {
    const amount = products.reduce((sum, item) => {
      const price = parseFloat(item.price) || 0;
      const quantity = parseInt(item.quantity) || 0;
      return sum + (price * quantity);
    }, 0).toFixed(2);
    
    // 返回格式化的金额
    return this.formatPrice(amount);
  },

  // 格式化价格，添加货币符号
  formatPrice(price) {
    if (!price) return '0';
    // 只返回数字，不添加人民币符号，由CSS样式添加
    return parseFloat(price).toFixed(2);
  },

  // 显示错误并返回
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none'
    });
    setTimeout(() => wx.navigateBack(), 1500);
  },

  // 获取默认地址
  getDefaultAddress() {
    console.log('获取默认地址');
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      console.log('未找到用户信息');
      return;
    }

    wx.showLoading({ title: '加载中' });
    db.collection('addresses')
      .where({
        _openid: userInfo._openid,
        isDefault: true
      })
      .get()
      .then(res => {
        wx.hideLoading();
        console.log('获取到地址数据:', res.data);
        if (res.data.length > 0) {
          this.setData({
            address: res.data[0]
          });
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('获取默认地址失败：', err);
      });
  },

  // 选择地址
  selectAddress() {
    console.log('跳转到地址选择页面');
    wx.navigateTo({
      url: '/pages/address/address?from=order'
    });
  },

  // 图片加载错误处理
  onImageError(e) {
    console.log('图片加载失败:', e);
    const index = e.currentTarget.dataset.index;
    const orderInfo = this.data.orderInfo;
    if (orderInfo && orderInfo.products && orderInfo.products[index]) {
      orderInfo.products[index].image = '/images/logo.png';
      this.setData({ orderInfo });
    }
  },

  // 加载用户优惠信息
  loadUserDiscounts() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo._openid) return;
    
    wx.showLoading({ title: '计算优惠中...' });
    
    // 1. 先计算促销活动折扣（强制应用）
    let promotionDiscount = 0;
    let promotionDetails = null;
    let promotionName = '';
    
    this.loadProductPromotions()
      .then(promotionResults => {
        // 保存促销活动结果
        if (promotionResults && promotionResults.finalPrice < parseFloat(this.data.totalAmount)) {
          promotionDiscount = (parseFloat(this.data.totalAmount) - promotionResults.finalPrice).toFixed(2);
          promotionDetails = promotionResults.discountDetails;
          
          if (promotionResults.discountDetails && promotionResults.discountDetails.length > 0) {
            promotionName = promotionResults.discountDetails[0].name;
          } else {
            promotionName = '促销优惠';
          }
        }
        
        // 2. 计算会员折扣（只在订单金额上应用，不与其他折扣叠加）
        const isMember = userInfo.memberLevel > 0;
        const memberDiscount = isMember ? (parseFloat(this.data.totalAmount) * 0.05).toFixed(2) : 0;
        
        // 3. 获取用户可用优惠券（用户可选）
        return db.collection('coupons')
          .where({
            userId: userInfo._openid,
            status: 0, // 未使用
            endTime: db.command.gt(new Date())
          })
          .get()
          .then(res => {
            const coupons = res.data || [];
            const availableCoupons = [];
            
            // 促销活动后的订单金额（用于计算优惠券折扣）
            const afterPromotionAmount = (parseFloat(this.data.totalAmount) - promotionDiscount).toFixed(2);
            
            // 计算每个优惠券可节省的金额
            coupons.forEach(coupon => {
              let saveAmount = 0;
              if (coupon.type === 'cash') {
                // 现金券直接减免
                saveAmount = parseFloat(coupon.value);
              } else if (coupon.type === 'percent') {
                // 折扣券按比例减免 (基于促销后的金额计算)
                saveAmount = (parseFloat(afterPromotionAmount) * (1 - parseFloat(coupon.value) / 100)).toFixed(2);
              }
              
              availableCoupons.push({
                id: coupon._id,
                name: coupon.name || (coupon.type === 'cash' ? `¥${coupon.value}元优惠券` : `${coupon.value}折优惠券`),
                saveAmount: saveAmount,
                source: '优惠券',
                couponData: coupon
              });
            });
            
            // 计算会员折扣（基于促销后的价格）
            const memberDiscountAmount = isMember ? (parseFloat(afterPromotionAmount) * 0.05).toFixed(2) : 0;
            
            // 如果是会员，添加会员折扣选项
            if (isMember) {
              availableCoupons.push({
                id: 'member_discount',
                name: '会员95折',
                saveAmount: memberDiscountAmount,
                source: '会员特权'
              });
            }
            
            // 按优惠金额排序（优惠最多的排在前面）
            availableCoupons.sort((a, b) => parseFloat(b.saveAmount) - parseFloat(a.saveAmount));
            
            // 设置促销活动信息（强制应用）
            const hasPromotion = promotionDiscount > 0;
            const promotionInfo = hasPromotion ? {
              id: 'promotion_discount',
              name: promotionName,
              saveAmount: promotionDiscount,
              source: '促销活动',
              promotionData: promotionDetails,
              isPromotion: true // 标记为促销活动，不可取消
            } : null;
            
            this.setData({
              promotionDiscount: hasPromotion ? promotionDiscount : 0, // 促销折扣金额
              promotionInfo: promotionInfo, // 促销信息
              discounts: availableCoupons, // 可选优惠（优惠券和会员折扣）
              isMember,
              memberDiscount: memberDiscountAmount
            });
            
            // 如果有促销优惠，强制应用
            if (hasPromotion) {
              this.applyPromotionDiscount();
            }
            
            // 如果有可用优惠券，自动选择最优惠的方案（但用户可以稍后更改）
            if (availableCoupons.length > 0) {
              const bestCoupon = availableCoupons[0];
              this.selectUserDiscount(bestCoupon);
            } else {
              // 没有可用优惠券，只应用促销折扣
              this.updateFinalPrice();
            }
            
            wx.hideLoading();
          });
      })
      .catch(err => {
        console.error('获取优惠信息失败:', err);
        wx.hideLoading();
      });
  },
  
  // 加载商品的促销信息
  loadProductPromotions() {
    if (!this.data.orderInfo || !this.data.orderInfo.products || this.data.orderInfo.products.length === 0) {
      return Promise.resolve(null);
    }
    
    // 获取所有商品ID
    const productIds = this.data.orderInfo.products.map(p => p.id || p._id);
    
    // 对每个商品计算促销价格
    const promotionPromises = productIds.map(productId => {
      if (!productId) return Promise.resolve(null);
      
      return promotionService.getProductPromotions(productId)
        .then(promotions => {
          if (!promotions || !promotions.data || promotions.data.length === 0) {
            return null;
          }
          
          // 找到对应的商品
          const product = this.data.orderInfo.products.find(p => (p.id || p._id) === productId);
          if (!product) return null;
          
          // 计算该商品的促销价格
          return promotionService.calculatePromotionPrice(
            parseFloat(product.price) * parseInt(product.quantity),
            promotions.data
          );
        })
        .catch(err => {
          console.error('计算商品促销价格失败:', err);
          return null;
        });
    });
    
    // 汇总所有商品的促销结果
    return Promise.all(promotionPromises)
      .then(results => {
        // 过滤掉无促销的商品
        const validResults = results.filter(r => r !== null);
        
        if (validResults.length === 0) {
          return null;
        }
        
        // 汇总所有优惠
        let totalDiscount = 0;
        const allDiscountDetails = [];
        
        validResults.forEach(result => {
          const originalPrice = parseFloat(this.data.totalAmount);
          const promotionPrice = result.finalPrice;
          const discount = originalPrice - promotionPrice;
          
          if (discount > 0) {
            totalDiscount += discount;
            
            // 收集优惠详情
            if (result.discountDetails && result.discountDetails.length > 0) {
              allDiscountDetails.push(...result.discountDetails);
            }
          }
        });
        
        // 计算最终价格
        const finalPrice = (parseFloat(this.data.totalAmount) - totalDiscount).toFixed(2);
        
        return {
          finalPrice: finalPrice,
          discountDetails: allDiscountDetails
        };
      });
  },
  
  // 应用促销折扣（强制的）
  applyPromotionDiscount() {
    if (!this.data.promotionInfo) return;
    
    const totalAmount = parseFloat(this.data.totalAmount);
    const promotionDiscount = parseFloat(this.data.promotionDiscount);
    
    // 更新优惠后的价格（只考虑促销活动）
    this.setData({
      afterPromotionAmount: (totalAmount - promotionDiscount).toFixed(2)
    });
    
    this.updateFinalPrice();
  },
  
  // 选择用户可选择的优惠（优惠券或会员折扣）
  selectUserDiscount(discount) {
    if (!discount) {
      this.setData({
        selectedDiscount: null,
        userDiscountAmount: 0
      });
      this.updateFinalPrice();
      return;
    }
    
    // 基于促销后的金额计算用户选择的优惠
    const baseAmount = this.data.afterPromotionAmount || this.data.totalAmount;
    const discountAmount = parseFloat(discount.saveAmount);
    
    this.setData({
      selectedDiscount: discount,
      userDiscountAmount: discountAmount.toFixed(2)
    });
    
    this.updateFinalPrice();
  },
  
  // 更新最终价格（综合所有优惠）
  updateFinalPrice() {
    const totalAmount = parseFloat(this.data.totalAmount);
    const promotionDiscount = parseFloat(this.data.promotionDiscount || 0);
    const userDiscount = parseFloat(this.data.userDiscountAmount || 0);
    
    // 计算总折扣和最终价格
    const totalDiscount = promotionDiscount + userDiscount;
    const finalPrice = Math.max(0.01, (totalAmount - totalDiscount).toFixed(2));
    
    // 使用格式化方法
    this.setData({
      finalPrice: this.formatPrice(finalPrice)
    });
  },
  
  // 选择优惠券或会员折扣
  chooseDiscount() {
    const discounts = this.data.discounts;
    if (!discounts || discounts.length === 0) {
      wx.showToast({
        title: '暂无可用优惠券',
        icon: 'none'
      });
      return;
    }
    
    const items = discounts.map(d => `${d.name} (省${d.saveAmount}元)`);
    items.push('不使用优惠券');
    
    wx.showActionSheet({
      itemList: items,
      success: (res) => {
        if (res.tapIndex < discounts.length) {
          // 选择了优惠券或会员折扣
          this.selectUserDiscount(discounts[res.tapIndex]);
        } else {
          // 选择不使用优惠券或会员折扣
          this.selectUserDiscount(null);
        }
      }
    });
  },

  // 提交订单
  submitOrder: function() {
    if (this.data.submitting) return;
    
    // 验证用户信息
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo._openid) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 验证地址
    if (!this.data.address) {
      wx.showToast({
        title: '请先选择收货地址',
        icon: 'none'
      });
      return;
    }
    
    // 验证商品
    if (!this.data.orderInfo || !this.data.orderInfo.products || !this.data.orderInfo.products.length) {
      wx.showToast({
        title: '订单商品信息异常',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ submitting: true });
    
    // 构建订单数据
    const orderData = {
      products: this.data.orderInfo.products,
      address: this.data.address,
      totalAmount: parseFloat(this.data.totalAmount),
      finalAmount: parseFloat(this.data.finalPrice || this.data.totalAmount),
      remark: this.data.remark || '',
      orderNo: this.generateOrderNo(),
      status: 0,  // 0-待付款, 1-已付款, 2-已发货, 3-已完成
      statusText: '待付款',
      userId: userInfo.dbUserId || userInfo._id,
      createTime: db.serverDate(),
      updateTime: db.serverDate(),
      statusHistory: [{
        status: 0,
        statusText: '待付款',
        time: new Date(),
        desc: '订单创建成功，等待付款'
      }]
    };
    
    // 添加优惠信息
    if (this.data.selectedDiscount) {
      orderData.discount = {
        id: this.data.selectedDiscount.id,
        name: this.data.selectedDiscount.name,
        amount: parseFloat(this.data.discountAmount),
        source: this.data.selectedDiscount.source
      };
      
      // 如果是优惠券，记录优惠券信息
      if (this.data.selectedDiscount.couponData) {
        orderData.couponId = this.data.selectedDiscount.id;
      }
      
      // 如果是促销活动，记录促销详情
      if (this.data.selectedDiscount.promotionData) {
        orderData.promotionDetails = this.data.selectedDiscount.promotionData;
      }
      
      // 计算实际折扣率，用于统计
      const discountRate = ((parseFloat(this.data.discountAmount) / parseFloat(this.data.totalAmount)) * 100).toFixed(2);
      orderData.discountRate = discountRate + '%';
    }
    
    // 添加优惠信息
    // 1. 促销活动信息（强制应用）
    if (this.data.promotionInfo) {
      orderData.promotion = {
        id: this.data.promotionInfo.id,
        name: this.data.promotionInfo.name,
        amount: parseFloat(this.data.promotionDiscount),
        source: this.data.promotionInfo.source
      };
      
      // 保存促销详情
      if (this.data.promotionInfo.promotionData) {
        orderData.promotionDetails = this.data.promotionInfo.promotionData;
      }
    }
    
    // 2. 用户选择的优惠（优惠券或会员）
    if (this.data.selectedDiscount) {
      orderData.userDiscount = {
        id: this.data.selectedDiscount.id,
        name: this.data.selectedDiscount.name,
        amount: parseFloat(this.data.userDiscountAmount),
        source: this.data.selectedDiscount.source
      };
      
      // 如果是优惠券，记录优惠券信息用于后续更新状态
      if (this.data.selectedDiscount.couponData) {
        orderData.couponId = this.data.selectedDiscount.id;
      }
    }
    
    // 3. 记录总优惠金额和折扣率
    if (this.data.totalDiscount > 0) {
      orderData.totalDiscount = parseFloat(this.data.totalDiscount);
      orderData.discountRate = ((parseFloat(this.data.totalDiscount) / parseFloat(this.data.totalAmount)) * 100).toFixed(2) + '%';
    }
    
    // 创建订单
    db.collection('orders')
      .add({
        data: orderData
      })
      .then(res => {
        const orderId = res._id;
        
        // 立即调用支付功能
        return wxpayService.createPayment({
          orderNo: orderData.orderNo,
          totalAmount: orderData.finalAmount || orderData.totalAmount,
          description: '支付订单'
        }).then(payParams => {
          // 发起支付
          return wxpayService.requestPayment(payParams).then(() => {
            // 支付成功，更新订单状态
            return db.collection('orders').doc(orderId).update({
              data: {
                status: 1,
                statusText: '待发货',
                payTime: db.serverDate(),
                statusHistory: db.command.push({
                  status: 1,
                  statusText: '待发货',
                  time: new Date(),
                  desc: '支付成功'
                }),
                updateTime: db.serverDate()
              }
            }).then(() => {
              // 如果使用了优惠券，更新优惠券状态
              if (orderData.couponId) {
                return db.collection('coupons').doc(orderData.couponId).update({
                  data: {
                    status: 1, // 标记为已使用
                    useTime: db.serverDate(),
                    orderId: orderId
                  }
                }).then(() => {
                  console.log('优惠券状态已更新');
                }).catch(err => {
                  console.error('更新优惠券状态失败:', err);
                });
              }
            }).then(() => {
              wx.showToast({
                title: '支付成功',
                icon: 'success'
              });
              
              setTimeout(() => {
                wx.redirectTo({
                  url: `/pages/order/detail/index?id=${orderId}&paid=1`,
                });
              }, 1000);
            });
          });
        }).catch(payErr => {
          console.error('支付过程发生错误:', payErr);
          
          // 用户取消支付或支付失败，跳转到订单详情页
          wx.showToast({
            title: payErr.errMsg && payErr.errMsg.indexOf('cancel') > -1 ? 
                   '支付已取消' : '支付失败',
            icon: 'none'
          });
          
          setTimeout(() => {
            wx.redirectTo({
              url: `/pages/order/detail/index?id=${orderId}`,
            });
          }, 1500);
        });
      })
      .catch(err => {
        console.error('创建订单失败:', err);
        wx.showToast({
          title: '订单提交失败',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ submitting: false });
      });
  },
  
  // 生成订单号
  generateOrderNo() {
    const now = new Date();
    return 'O' + now.getFullYear() +
      String(now.getMonth() + 1).padStart(2, '0') +
      String(now.getDate()).padStart(2, '0') +
      String(now.getHours()).padStart(2, '0') +
      String(now.getMinutes()).padStart(2, '0') +
      String(now.getSeconds()).padStart(2, '0') +
      String(Math.floor(Math.random() * 1000)).padStart(3, '0');
  }
})