const db = wx.cloud.database()

Page({
  data: {
    addressList: [],
    fromOrder: false,  // 是否从订单页面跳转来
    loading: true
  },

  onLoad(options) {
    this.setData({
      fromOrder: options.from === 'order'
    });
    this.getAddressList();
  },

  // 获取地址列表
  getAddressList() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.dbUserId) {
      console.log('[验证] 用户未登录');
      this.setData({ 
        loading: false,
        addressList: []
      });
      wx.hideLoading();
      return;
    }

    wx.showLoading({ title: '加载中' });

    // 使用云函数获取地址列表
    wx.cloud.callFunction({
      name: 'baseFunction',
      data: {
        action: 'getAddressList',
        data: {
          userId: userInfo.dbUserId
        }
      }
    })
    .then(res => {
      if (res.result.code === 0 && res.result.data) {
        console.log('[云函数] 获取地址成功:', res.result.data.length);
        this.setData({
          addressList: res.result.data,
          loading: false
        });
      } else {
        console.error('[云函数] 获取地址失败:', res.result.msg);
        this.setData({ 
          loading: false,
          addressList: []
        });
        wx.showToast({
          title: '获取地址失败',
          icon: 'none'
        });
      }
    })
    .catch(err => {
      console.error('[云函数] 获取地址列表失败：', err);
      this.setData({ 
        loading: false,
        addressList: []
      });
      wx.showToast({
        title: '获取地址失败',
        icon: 'error'
      });
    })
    .finally(() => {
      wx.hideLoading();
    });
  },

  // 选择地址
  selectAddress(e) {
    if (!this.data.fromOrder) return;
    
    const id = e.currentTarget.dataset.id;
    const address = this.data.addressList.find(item => item._id === id);
    
    // 返回订单页面并传递地址信息
    const pages = getCurrentPages();
    const orderPage = pages[pages.length - 2];
    
    if (orderPage && orderPage.route === 'pages/order/confirm') {
      orderPage.setData({
        address: address
      });
      wx.navigateBack();
    }
  },

  // 创建新地址
  createAddress() {
    wx.navigateTo({
      url: '/pages/address/create'
    });
  },

  // 编辑地址
  editAddress(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/address/create?id=${id}`
    });
  },

  // 设置默认地址
  setDefaultAddress(id) {
    wx.showLoading({ title: '设置中' });
    // 先将所有地址设为非默认
    db.collection('addresses')
      .where({
        userId: wx.getStorageSync('userInfo').dbUserId
      })
      .update({
        data: {
          isDefault: false
        }
      })
      .then(() => {
        // 再将选中的地址设为默认
        return db.collection('addresses').doc(id).update({
          data: {
            isDefault: true
          }
        });
      })
      .then(() => {
        wx.hideLoading();
        this.getAddressList();
      })
      .catch(err => {
        console.error('设置默认地址失败：', err);
        wx.hideLoading();
        wx.showToast({
          title: '设置失败',
          icon: 'error'
        });
      });
  },

  // 删除地址
  deleteAddress(e) {
    const { id } = e.currentTarget.dataset;
    const userInfo = wx.getStorageSync('userInfo');
    
    if (!userInfo || !userInfo.dbUserId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '提示',
      content: '确定要删除这个地址吗？',
      success: (res) => {
        if (!res.confirm) return;

        wx.showLoading({ 
          title: '删除中...',
          mask: true
        });

        // 使用云函数删除地址
        wx.cloud.callFunction({
          name: 'baseFunction',
          data: {
            action: 'deleteAddress',
            data: {
              addressId: id
            }
          }
        })
        .then(res => {
          if (res.result.code === 0) {
            // 更新本地数据
            const newAddressList = this.data.addressList.filter(item => item._id !== id);
            this.setData({
              addressList: newAddressList
            }, () => {
              wx.hideLoading();
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            });
          } else {
            wx.hideLoading();
            wx.showToast({
              title: res.result.msg || '删除失败',
              icon: 'error'
            });
          }
        })
        .catch(err => {
          wx.hideLoading();
          console.error('[云函数] 删除地址失败:', {
            错误: err.message,
            错误类型: err.name,
            错误堆栈: err.stack,
            地址ID: id,
            用户ID: userInfo.dbUserId,
            时间: new Date().toISOString()
          });
          
          wx.showToast({
            title: '删除失败',
            icon: 'error',
            duration: 2000
          });
        });
      }
    });
  }
}); 