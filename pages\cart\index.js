// pages/cart/index.js
const db = wx.cloud.database()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    cartList: [],
    allSelected: false,
    totalPrice: '0.00',
    selectedCount: 0,
    loading: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('[生命周期] 页面加载, 参数:', options);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('[生命周期] 页面显示');
    this.getCartList();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 获取购物车列表
  async getCartList() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.dbUserId) {
      console.log('[购物车] 用户未登录');
      this.setData({ loading: false });
      return;
    }

    wx.showLoading({ title: '加载中' });
    
    try {
      const cartRes = await db.collection('cart')
        .where({
          userId: userInfo.dbUserId,
          isDeleted: false
        })
        .orderBy('createTime', 'desc')
        .get();

      console.log('[购物车] 获取到的数据:', cartRes.data);

      // 处理新旧数据结构，统一格式
      const cartList = cartRes.data.map(item => {
        // 处理旧数据结构（有productId的情况）
        if (item.productId && !item.product) {
          // 标记为已删除而不是直接删除
          db.collection('cart').doc(item._id).update({
            data: {
              isDeleted: true
            }
          }).then(() => {
            console.log('[购物车] 标记旧数据:', item._id);
          }).catch(err => {
            console.error('[购物车] 标记旧数据失败:', err);
          });
          return null;
        }

        // 处理新数据结构
        if (!item.product) {
          console.error('[购物车] 商品数据不完整:', item);
          return null;
        }

        // 确保规格信息完整
        const specs = {
          name: item.specs?.name || '默认规格',
          price: item.specs?.price || item.product.price || 0
        };

        return {
          ...item,
          product: {
            _id: item.product._id,
            name: item.product.name,
            image: item.product.image,
            price: specs.price,
            specs: specs.name // 只传递规格名称用于显示
          }
        };
      }).filter(Boolean); // 过滤掉无效数据

      console.log('[购物车] 处理后的数据:', cartList);

      this.setData({
        cartList,
        loading: false
      }, () => {
        this.calculateTotal();
      });
      wx.hideLoading();
      
    } catch (err) {
      console.error('[购物车] 获取失败:', err);
      this.setData({ loading: false });
      wx.hideLoading();
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  // 清理无效的购物车数据
  cleanInvalidCartItems(cartItems) {
    console.log('[数据库] 开始清理无效购物车数据');
    const invalidItems = cartItems.filter(item => !item.product || !item.product.name);
    const userInfo = wx.getStorageSync('userInfo');
    
    invalidItems.forEach(item => {
      console.log('[数据库] 删除无效数据:', item._id);
      db.collection('cart')
        .where({
          _id: item._id,
          _openid: userInfo._openid
        })
        .remove()
        .then(() => {
          console.log('[数据库] 成功删除无效数据:', item._id);
        })
        .catch(err => {
          console.error('[数据库] 删除失败:', err);
        });
    });
  },

  // 更新购物车徽标
  updateCartBadge(count) {
    const pages = getCurrentPages();
    const shopPage = pages.find(p => p.route === 'pages/shop/shop');
    if (shopPage) {
      shopPage.getCartCount();
    }
  },

  // 切换商品选中状态
  toggleSelect(e) {
    const { id } = e.currentTarget.dataset;
    const { cartList } = this.data;
    const index = cartList.findIndex(item => item._id === id);
    
    // 创建新的数组以确保视图更新
    const newCartList = [...cartList];
    newCartList[index].selected = !newCartList[index].selected;
    
    this.setData({
      cartList: newCartList,
      allSelected: newCartList.every(item => item.selected)
    });
    
    this.calculateTotal();
  },

  // 切换全选状态
  toggleSelectAll() {
    const { cartList, allSelected } = this.data;
    
    // 创建新的数组以确保视图更新
    const newCartList = cartList.map(item => ({
      ...item,
      selected: !allSelected
    }));
    
    this.setData({
      cartList: newCartList,
      allSelected: !allSelected
    });
    
    this.calculateTotal();
  },

  // 更新商品数量
  updateQuantity(e) {
    const { id, type } = e.currentTarget.dataset;
    const { cartList } = this.data;
    const index = cartList.findIndex(item => item._id === id);
    
    if (type === 'minus' && cartList[index].quantity > 1) {
      cartList[index].quantity--;
    } else if (type === 'plus') {
      cartList[index].quantity++;
    }
    
    this.setData({ cartList });
    this.calculateTotal();
    this.updateCartItem(id, cartList[index].quantity);
  },

  // 更新购物车商品数量
  updateCartItem(id, quantity) {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.dbUserId) {
      console.error('[购物车] 用户未登录或无效');
      return;
    }

    return db.collection('cart')
      .doc(id)
      .update({
        data: {
          quantity: quantity,
          updateTime: db.serverDate()
        }
      })
      .then(() => {
        console.log('[数据库] 更新数量成功:', {
          id,
          quantity
        });
      })
      .catch(err => {
        console.error('[数据库] 更新数量失败:', err);
        wx.showToast({
          title: '更新失败',
          icon: 'error'
        });
      });
  },

  // 删除购物车商品
  deleteCartItem(e) {
    console.log('[操作] 开始删除购物车商品');
    const { id } = e.currentTarget.dataset;
    const userInfo = wx.getStorageSync('userInfo');
    
    if (!userInfo || !userInfo.dbUserId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '提示',
      content: '确定要删除这个商品吗？',
      success: (res) => {
        if (!res.confirm) return;

        wx.showLoading({ 
          title: '删除中...',
          mask: true
        });

        // 直接使用数据库操作删除
        db.collection('cart')
          .doc(id)
          .remove()
          .then(() => {
            // 更新本地数据
            const newCartList = this.data.cartList.filter(item => item._id !== id);
            
            this.setData({
              cartList: newCartList
            }, () => {
              this.calculateTotal();
              // 更新购物车徽标
              this.updateCartBadge(newCartList.length);
              wx.hideLoading();
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            });
          })
          .catch(err => {
            wx.hideLoading();
            console.error('[数据库] 删除商品失败:', {
              错误: err.message,
              错误类型: err.name,
              错误堆栈: err.stack,
              商品ID: id,
              用户ID: userInfo.dbUserId,
              时间: new Date().toISOString()
            });
            
            wx.showToast({
              title: '删除失败',
              icon: 'error',
              duration: 2000
            });
          });
      }
    });
  },

  // 计算总价
  calculateTotal() {
    const { cartList } = this.data;
    const selectedItems = cartList.filter(item => item.selected);
    const total = selectedItems.reduce((sum, item) => {
      return sum + (item.product.price * item.quantity);
    }, 0);
    
    this.setData({
      totalPrice: total.toFixed(2),
      selectedCount: selectedItems.length
    });
  },

  // 结算按钮点击
  checkout() {
    const selectedProducts = this.data.cartList.filter(item => item.selected).map(item => ({
      _id: item.product._id,
      name: item.product.name,
      price: item.product.price,
      quantity: item.quantity,
      image: item.product.image,
      specs: item.specs  // 确保这里有传递规格信息
    }));
    
    wx.navigateTo({
      url: '/pages/order/confirm',
      success: function(res) {
        res.eventChannel.emit('acceptDataFromOpenerPage', {
          products: selectedProducts
        });
      }
    });
  },

  // 跳转到商城
  goToShop() {
    wx.switchTab({
      url: '/pages/shop/shop'
    });
  },

  // 选择商品
  selectItem(e) {
    const { index } = e.currentTarget.dataset;
    const cartList = this.data.cartList;
    cartList[index].selected = !cartList[index].selected;
    
    // 计算选中商品数量和总价
    const selectedCount = cartList.filter(item => item.selected).length;
    const totalPrice = cartList
      .filter(item => item.selected)
      .reduce((total, item) => total + item.product.price * item.quantity, 0)
      .toFixed(2);

    this.setData({
      cartList,
      selectedCount,
      totalPrice
    });
  },

  // 修改商品数量
  changeQuantity(e) {
    const { index, type } = e.currentTarget.dataset;
    const cartList = this.data.cartList;
    const item = cartList[index];

    if (type === 'minus' && item.quantity > 1) {
      item.quantity--;
    } else if (type === 'plus') {
      item.quantity++;
    }

    // 更新数据库
    db.collection('cart').doc(item._id).update({
      data: {
        quantity: item.quantity
      }
    }).then(() => {
      // 重新计算总价
      const totalPrice = cartList
        .filter(item => item.selected)
        .reduce((total, item) => total + item.product.price * item.quantity, 0)
        .toFixed(2);

      this.setData({
        cartList,
        totalPrice
      });
    }).catch(err => {
      console.error('更新购物车数量失败：', err);
    });
  },

  // 添加商品到购物车时的数据结构
  addToCart() {
    const cartData = {
      product: {
        _id: product._id,
        name: product.name,
        price: product.price,
        image: product.image || '/images/商品默认图.png'
      },
      specs: selectedSpecs,
      quantity: quantity,
      selected: false,  // 默认未选中
      createTime: db.serverDate()
    };

    db.collection('cart').add({
      data: cartData
    });
  }
})