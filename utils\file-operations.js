const fs = wx.getFileSystemManager();

// 创建目录函数
const createDirectories = () => {
  const dirs = [
    'assets/images',
    'assets/styles',
    'packages/mall/pages/product-list',
    'packages/mall/pages/product-detail',
    'packages/user/pages/settings',
    'packages/user/pages/profile'
  ];

  dirs.forEach(dir => {
    try {
      fs.mkdirSync(dir, true);
      console.log(`创建目录成功: ${dir}`);
    } catch (err) {
      console.error(`创建目录失败: ${dir}`, err);
    }
  });
};

// 复制并重命名文件函数
const copyAndRenameFiles = () => {
  const fileRenames = [
    {
      oldPath: '/images/品牌中心-未选中.png',
      newPath: '/assets/images/home-default.png'
    },
    {
      oldPath: '/images/品牌中心-选中.png',
      newPath: '/assets/images/home-active.png'
    },
    {
      oldPath: '/images/品牌商城-未选中.png',
      newPath: '/assets/images/cart-default.png'
    },
    {
      oldPath: '/images/品牌商城-选中.png',
      newPath: '/assets/images/cart-active.png'
    },
    {
      oldPath: '/images/个人中心-未选中.png',
      newPath: '/assets/images/user-default.png'
    },
    {
      oldPath: '/images/个人中心-选中.png',
      newPath: '/assets/images/user-active.png'
    }
  ];

  fileRenames.forEach(({oldPath, newPath}) => {
    try {
      // 获取小程序运行路径
      const rootPath = wx.env.USER_DATA_PATH;
      const fullOldPath = rootPath + oldPath;
      const fullNewPath = rootPath + newPath;

      // 确保目标目录存在
      const dir = fullNewPath.substring(0, fullNewPath.lastIndexOf('/'));
      try {
        fs.accessSync(dir);
      } catch (e) {
        fs.mkdirSync(dir, true);
      }
      
      // 读取原文件
      const fileData = fs.readFileSync(fullOldPath);
      // 写入新文件
      fs.writeFileSync(fullNewPath, fileData);
      console.log(`复制并重命名成功: ${oldPath} -> ${newPath}`);
    } catch (err) {
      console.error(`复制并重命名失败: ${oldPath}`, err);
      console.error('错误详情:', err);
    }
  });
};

// 执行所有操作
const restructureProject = () => {
  try {
    // 1. 创建新目录
    createDirectories();
    
    // 2. 复制并重命名文件
    copyAndRenameFiles();
    
    console.log('项目重构完成！');
  } catch (err) {
    console.error('项目重构失败:', err);
  }
};

// 导出函数供其他文件使用
module.exports = {
  restructureProject
}; 