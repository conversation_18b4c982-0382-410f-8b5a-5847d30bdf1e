/* 页面根容器 */
page {
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow: hidden;
}

/* 加载状态样式 */
.loading-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #A67B5B;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

/* 轮播图容器 - 使用相对定位
.banner-swiper {
  width: 100vw;
  margin: 0;
  padding: 0;
  position: re/atv

.banner-item {
  width: 100vw;
  height: 100%;
  margin: 0;
  padding: 0;
}

.banner-image {
  width: 100vw;
  height: 100%;
  margin: 0;
  padding: 0;
  display: block;
  object-fit: cover;
}

/* 自定义指示点样式 */
.wx-swiper-dot {
  width: 40rpx !important;
  height: 4rpx !important;
  border-radius: 0 !important;
  background: rgba(255, 255, 255, 0.3) !important;
}

.wx-swiper-dot-active {
  background: #ffffff !important;
}

/* 网络错误提示样式 */
.network-error {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: #FFF9E6;
  border-bottom: 1rpx solid #FFE7BA;
  box-sizing: border-box;
}

.error-text {
  font-size: 24rpx;
  color: #E6A23C;
}

.retry-button {
  font-size: 24rpx;
  color: #A67B5B;
  padding: 6rpx 20rpx;
  border: 1rpx solid #A67B5B;
  border-radius: 30rpx;
}

/* 旧的样式 */
.nav-placeholder {
  height: calc(44px + env(safe-area-inset-top));
  width: 100%;
}

.container {
  padding: 0;
}

.content {
  background: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
}

.content text {
  display: block;
  margin-bottom: 10rpx;
}

.search-box {
  margin: 20rpx 30rpx;
  height: 80rpx;
  background: #ffffff;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-icon {
  margin-right: 20rpx;
  font-size: 32rpx;
}

.search-box input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
}

.placeholder {
  color: #999;
}

.main-content {
  flex: 1;
  padding: 30rpx;
  height: calc(100vh - 180rpx - env(safe-area-inset-top));
}

.banner-section {
  width: 100%;
  height: 400rpx;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 20rpx;
  padding: 40rpx;
  color: #ffffff;
  margin-bottom: 40rpx;
}

.banner-swiper {
  width: 100%;
  height: 100%;
}

.banner-content {
  position: relative;
}

.banner-text {
  margin-bottom: 30rpx;
}

.banner-title {
  font-size: 40rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.banner-subtitle {
  font-size: 36rpx;
  display: block;
  margin-bottom: 10rpx;
}

.banner-desc {
  font-size: 28rpx;
  opacity: 0.8;
  display: block;
}

.banner-image {
  width: 100%;
  height: 100%;
  margin: 20rpx 0;
}

.banner-tag {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.series-section {
  margin-top: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: var(--text-color);
}

.series-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.series-item {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding: 20rpx;
}

.series-item image {
  width: 100%;
  height: 200rpx;
  object-fit: cover;
}

.series-info {
  padding: 20rpx;
}

.series-name {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 8rpx;
}

.series-desc {
  font-size: 24rpx;
  color: var(--text-color-light);
  display: block;
}

swiper {
  width: 100%;
  height: 100vh;
}

swiper-item image {
  width: 100%;
  height: 100%;
}


.banner {
  width: 100%;
  height: 100vh;
}


.banner image {
  width: 100%;
  height: 100%;
}


.wx-swiper-dot {
  width: 40rpx !important;
  height: 4rpx !important;
  border-radius: 0 !important;
 
 background: rgba(255, 255, 255, 0.3) !important;
}

.wx-swiper-dot-active {
  background: #ffffff !important;
}
























