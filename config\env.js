/**
 * 小程序环境配置
 */
const ENV = {
  // 核心配置
  CLOUD_ENV: 'cloud1-2gfror0a1b871162',
  APP_ID: 'wxf57755081128c53d',
  
  // API基础URL
  BASE_URL: 'https://your-api-domain.com',
  
  // 云函数配置
  CLOUD_FUNCTIONS: {
    LOGIN: 'login',
    GET_OPENID: 'getOpenId',
    GET_PRODUCTS: 'getProducts',
    GET_BANNERS: 'getBanners',
    CREATE_ORDER: 'createOrder',
    GET_ORDERS: 'getOrders',
    WXPAY: 'wxpayFunctions'
  },
  
  // 数据库集合名称
  COLLECTIONS: {
    USERS: 'users',
    PRODUCTS: 'products',
    ORDERS: 'orders',
    BANNERS: 'banners',
    CATEGORIES: 'categories',
    CART: 'cart'
  },
  
  // 图片存储路径配置
  STORAGE_PATH: {
    BANNER: 'images/轮播/',
    AVATAR: 'images/默认头像.png',
    PRODUCT: 'products/',
    DETAIL: 'products/detail/',
    USER_UPLOAD: 'user-uploads/',
    TEMP: 'temp/'
  },
  
  // 默认图片配置
  DEFAULT_IMAGES: {
    BANNER: '/images/轮播/轮播1.jpg',
    BANNER_LIST: [  // 新增：轮播图列表
      '/images/轮播/轮播1.jpg',
      '/images/轮播/轮播2.jpg',
      '/images/轮播/轮播3.jpg'
    ],
    AVATAR: '/images/默认头像.png',
    PRODUCT: '/images/default-product.png',
    PLACEHOLDER: '/images/placeholder.png',
    ERROR: '/images/error.png'
  },
  
  // 分页配置
  PAGE_SIZE: {
    PRODUCTS: 10,
    ORDERS: 20,
    BANNERS: 5
  },
  
  // 缓存配置
  CACHE: {
    EXPIRE_TIME: 30 * 60 * 1000, // 30分钟
    MAX_SIZE: 50
  },
  
  // 支付配置
  PAYMENT: {
    TIMEOUT: 30 * 60 * 1000, // 30分钟超时
    RETRY_COUNT: 3
  },
  
  // 开发模式配置
  DEBUG: true,
  LOG_LEVEL: 'info'
};

module.exports = ENV;
