const db = wx.cloud.database()

Page({
  data: {
    hasReceived: false,
    expiryDate: ''
  },

  onLoad() {
    this.checkCouponStatus();
  },

  // 检查用户是否已领取优惠券
  checkCouponStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) return;

    db.collection('coupons')
      .where({
        _openid: userInfo._openid,
        type: 'member',
        amount: 30,
        minAmount: 100
      })
      .get()
      .then(res => {
        if (res.data.length > 0) {
          this.setData({
            hasReceived: true,
            expiryDate: this.formatDate(res.data[0].endDate)
          });
        }
      });
  },

  // 领取优惠券
  receiveCoupon() {
    if (this.data.hasReceived) return;
    
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '领取中' });

    // 计算7天后的日期
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 7);

    // 添加优惠券到用户账户
    db.collection('coupons').add({
      data: {
        type: 'member',
        name: '会员专享券',
        amount: 30,
        minAmount: 100,
        isUsed: 'false',
        createTime: db.serverDate(),
        endDate: endDate
      }
    })
    .then(() => {
      wx.hideLoading();
      wx.showToast({
        title: '领取成功',
        icon: 'success'
      });
      this.setData({
        hasReceived: true,
        expiryDate: this.formatDate(endDate)
      });
    })
    .catch(err => {
      console.error('领取优惠券失败：', err);
      wx.hideLoading();
      wx.showToast({
        title: '领取失败',
        icon: 'error'
      });
    });
  },

  // 格式化日期
  formatDate(date) {
    date = new Date(date);
    return `${date.getFullYear()}.${String(date.getMonth() + 1).padStart(2, '0')}.${String(date.getDate()).padStart(2, '0')}`;
  }
}) 