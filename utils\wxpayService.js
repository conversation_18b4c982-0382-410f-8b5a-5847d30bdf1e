/**
 * 微信支付服务 - 基于云函数实现
 * 使用微信官方的支付云函数模块实现
 */
const wxpayService = {
  /**
   * 创建支付订单
   * @param {Object} orderData 订单数据
   * @param {String} orderData.orderNo 订单号
   * @param {Number} orderData.totalAmount 订单金额（元）
   * @param {Boolean} orderData.mockPayment 是否使用模拟支付
   * @param {Boolean} orderData.forceRealPayment 是否强制真实支付
   * @returns {Promise<Object>} 支付参数
   */
  async createPayment(orderData) {
    if (!orderData.orderNo) {
      throw new Error('订单号不能为空');
    }
    
    if (!orderData.totalAmount || isNaN(orderData.totalAmount) || orderData.totalAmount <= 0) {
      throw new Error('无效的订单金额');
    }
    
    // 计算支付金额（分）
    const totalFee = Math.floor(orderData.totalAmount * 100);
    
    // 获取环境信息
    const accountInfo = wx.getAccountInfoSync();
    const envVersion = accountInfo?.miniProgram?.envVersion || 'release';
    
    // 环境支付策略配置
    const ENV_PAYMENT_CONFIG = {
      develop: { useMock: true, forceReal: false }, // 开发环境使用模拟支付
      trial: { useMock: true, forceReal: false },   // 体验版使用模拟支付
      review: { useMock: false, forceReal: true },  // 审核版使用真实支付
      release: { useMock: false, forceReal: true }  // 正式版使用真实支付
    };
    
    // 获取当前环境配置，默认为正式环境配置
    const envConfig = ENV_PAYMENT_CONFIG[envVersion] || ENV_PAYMENT_CONFIG.release;
    
    // 允许通过参数覆盖环境配置
    const useMockPayment = orderData.mockPayment !== undefined ? 
                           orderData.mockPayment : 
                           envConfig.useMock;
                           
    const forceRealPayment = orderData.forceRealPayment !== undefined ? 
                             orderData.forceRealPayment : 
                             envConfig.forceReal;
    
    console.log('[微信支付服务] 创建支付订单:', {
      订单号: orderData.orderNo,
      金额: totalFee,
      环境: envVersion,
      模拟支付: useMockPayment ? '是' : '否',
      强制真实支付: forceRealPayment ? '是' : '否'
    });
    
    try {
      // 获取用户OpenID
      const userInfo = wx.getStorageSync('userInfo') || {};
      const openid = userInfo._openid;
      
      if (!openid) {
        console.warn('[微信支付服务] 未找到用户OpenID');
      }
      
      const res = await wx.cloud.callFunction({
        name: 'wxpayFunctions',
        data: {
          action: 'unifiedOrder',
          orderNo: orderData.orderNo,
          totalFee: totalFee,
          openid: openid,
          mockPayment: useMockPayment,
          forceRealPayment: forceRealPayment
        }
      });
      
      console.log('[微信支付服务] 统一下单返回:', res);
      
      if (!res.result || res.result.code !== 0) {
        throw new Error(res.result?.message || '创建支付订单失败');
      }
      
      // 返回支付参数
      const paymentParams = res.result.data;
      
      // 记录支付参数
      console.log('[微信支付服务] 支付参数:', {
        ...paymentParams,
        paySign: '(签名已隐藏)'
      });
      
      return paymentParams;
    } catch (err) {
      console.error('[微信支付服务] 创建支付订单失败:', err);
      throw err;
    }
  },
  
  /**
   * 发起支付
   * @param {Object} paymentParams 支付参数
   * @returns {Promise<Object>} 支付结果
   */
  requestPayment(paymentParams) {
    // 验证支付参数
    if (!this.validatePayParams(paymentParams)) {
      throw new Error('无效的支付参数');
    }
    
    console.log('[微信支付服务] 发起支付请求:', {
      ...paymentParams,
      paySign: '(签名已隐藏)'
    });
    
    // 确保参数都是字符串类型
    const params = {
      timeStamp: String(paymentParams.timeStamp),
      nonceStr: String(paymentParams.nonceStr),
      package: String(paymentParams.package),
      signType: String(paymentParams.signType),
      paySign: String(paymentParams.paySign)
    };
    
    // 检查是否为模拟支付
    const isMockPayment = paymentParams._mock === true || 
      (paymentParams.paySign && paymentParams.paySign.indexOf('MOCK_SIGNATURE_') === 0);
    
    return new Promise((resolve, reject) => {
      if (isMockPayment) {
        console.log('[微信支付服务] 检测到模拟支付，使用模拟支付流程');
        
        // 计算显示金额
        const showAmount = (paymentParams.total_fee / 100).toFixed(2);
        
        // 模拟支付确认弹窗
        wx.showModal({
          title: '模拟支付',
          content: `确认支付 ${showAmount} 元？\n(开发环境模拟支付)`,
          cancelText: '取消支付',
          confirmText: '确认支付',
          success(res) {
            if (res.confirm) {
              // 模拟支付成功
              console.log('[微信支付服务] 模拟支付成功');
              resolve({
                errMsg: "requestPayment:ok",
                isMockPayment: true
              });
            } else {
              // 模拟用户取消支付
              console.log('[微信支付服务] 用户取消模拟支付');
              reject({
                errMsg: "requestPayment:fail cancel"
              });
            }
          }
        });
        return;
      }
      
      // 真实支付流程
      wx.requestPayment({
        ...params,
        success(res) {
          console.log('[微信支付服务] 支付成功:', res);
          resolve({
            ...res,
            isMockPayment: false
          });
        },
        fail(err) {
          console.error('[微信支付服务] 支付失败:', err);
          reject(err);
        }
      });
    });
  },
  
  /**
   * 查询订单
   * @param {String} orderNo 订单号
   * @returns {Promise<Object>} 订单信息
   */
  async queryOrder(orderNo) {
    if (!orderNo) {
      throw new Error('订单号不能为空');
    }
    
    console.log('[微信支付服务] 查询订单:', orderNo);
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'wxpayFunctions',
        data: {
          action: 'queryOrder',
          orderNo: orderNo
        }
      });
      
      console.log('[微信支付服务] 查询订单返回:', res);
      
      if (!res.result || res.result.code !== 0) {
        throw new Error(res.result?.message || '查询订单失败');
      }
      
      return res.result.data;
    } catch (err) {
      console.error('[微信支付服务] 查询订单失败:', err);
      throw err;
    }
  },
  
  /**
   * 关闭订单
   * @param {String} orderNo 订单号
   * @returns {Promise<Object>} 关闭结果
   */
  async closeOrder(orderNo) {
    if (!orderNo) {
      throw new Error('订单号不能为空');
    }
    
    console.log('[微信支付服务] 关闭订单:', orderNo);
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'wxpayFunctions',
        data: {
          action: 'closeOrder',
          orderNo: orderNo
        }
      });
      
      console.log('[微信支付服务] 关闭订单返回:', res);
      
      if (!res.result || res.result.code !== 0) {
        throw new Error(res.result?.message || '关闭订单失败');
      }
      
      return res.result.data;
    } catch (err) {
      console.error('[微信支付服务] 关闭订单失败:', err);
      throw err;
    }
  },
  
  /**
   * 申请退款
   * @param {Object} refundData 退款数据
   * @param {String} refundData.orderNo 订单号
   * @param {Number} refundData.totalFee 订单金额（分）
   * @param {Number} refundData.refundFee 退款金额（分）
   * @param {String} refundData.refundReason 退款原因
   * @returns {Promise<Object>} 退款结果
   */
  async refund(refundData) {
    if (!refundData.orderNo) {
      throw new Error('订单号不能为空');
    }
    
    if (!refundData.totalFee || refundData.totalFee <= 0) {
      throw new Error('无效的订单金额');
    }
    
    if (!refundData.refundFee || refundData.refundFee <= 0) {
      throw new Error('无效的退款金额');
    }
    
    console.log('[微信支付服务] 申请退款:', refundData);
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'wxpayFunctions',
        data: {
          action: 'refund',
          refundParams: {
            out_trade_no: refundData.orderNo,
            total_fee: refundData.totalFee,
            refund_fee: refundData.refundFee,
            refund_desc: refundData.refundReason || '用户申请退款'
          }
        }
      });
      
      console.log('[微信支付服务] 申请退款返回:', res);
      
      if (!res.result || res.result.code !== 0) {
        throw new Error(res.result?.message || '申请退款失败');
      }
      
      return res.result.data;
    } catch (err) {
      console.error('[微信支付服务] 申请退款失败:', err);
      throw err;
    }
  },
  
  /**
   * 查询退款
   * @param {String} refundId 退款单号
   * @returns {Promise<Object>} 退款信息
   */
  async queryRefund(refundId) {
    if (!refundId) {
      throw new Error('退款单号不能为空');
    }
    
    console.log('[微信支付服务] 查询退款:', refundId);
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'wxpayFunctions',
        data: {
          action: 'queryRefund',
          refundId: refundId
        }
      });
      
      console.log('[微信支付服务] 查询退款返回:', res);
      
      if (!res.result || res.result.code !== 0) {
        throw new Error(res.result?.message || '查询退款失败');
      }
      
      return res.result.data;
    } catch (err) {
      console.error('[微信支付服务] 查询退款失败:', err);
      throw err;
    }
  },
  
  /**
   * 验证支付参数
   * @param {Object} params 支付参数
   * @returns {Boolean} 是否有效
   */
  validatePayParams(params) {
    const requiredParams = ['timeStamp', 'nonceStr', 'package', 'signType', 'paySign'];
    
    // 检查参数是否存在
    const missingParams = requiredParams.filter(key => !params[key]);
    if (missingParams.length > 0) {
      console.error('[微信支付服务] 缺少支付参数:', missingParams);
      return false;
    }
    
    // 检查参数类型
    const invalidParams = requiredParams.filter(key => 
      typeof params[key] !== 'string' || !params[key].length
    );
    if (invalidParams.length > 0) {
      console.error('[微信支付服务] 支付参数类型错误:', invalidParams);
      return false;
    }
    
    return true;
  },
  
  /**
   * 检查是否为模拟支付
   * @param {Object} paymentParams 支付参数
   * @returns {Boolean} 是否为模拟支付
   */
  isMockPayment(paymentParams) {
    return paymentParams._mock === true || 
      (paymentParams.paySign && paymentParams.paySign.indexOf('MOCK_SIGNATURE_') === 0);
  },
  
  /**
   * 获取当前小程序环境信息
   * @returns {Object} 环境信息
   */
  getEnvironmentInfo() {
    try {
      const accountInfo = wx.getAccountInfoSync();
      return {
        envVersion: accountInfo?.miniProgram?.envVersion || 'release',
        appId: accountInfo?.miniProgram?.appId,
        isDevelop: accountInfo?.miniProgram?.envVersion === 'develop',
        isTrial: accountInfo?.miniProgram?.envVersion === 'trial',
        isReview: accountInfo?.miniProgram?.envVersion === 'review',
        isRelease: accountInfo?.miniProgram?.envVersion === 'release' || !accountInfo?.miniProgram?.envVersion,
        isProduction: ['review', 'release'].includes(accountInfo?.miniProgram?.envVersion)
      };
    } catch (err) {
      console.error('[微信支付服务] 获取环境信息失败:', err);
      // 默认返回生产环境
      return {
        envVersion: 'release',
        isDevelop: false,
        isTrial: false,
        isReview: false,
        isRelease: true,
        isProduction: true
      };
    }
  }
};

export default wxpayService; 