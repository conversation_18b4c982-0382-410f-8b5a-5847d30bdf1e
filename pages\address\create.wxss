.container {
  min-height: 100vh;
  background: var(--background-color);
  padding-bottom: 140rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.page-title {
  font-size: 32rpx;
  color: var(--text-color);
  font-weight: 500;
  padding: 30rpx 30rpx 10rpx;
}

.form-group {
  background: #fff;
  margin: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  position: relative;
  z-index: 1;
}

.line {
  position: absolute;
  left: 32rpx;
  right: 32rpx;
  bottom: 0;
  height: 1rpx;
  background: #f0f0f0;
}

.form-item:last-child .line {
  display: none;
}

.label {
  width: 160rpx;
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: 500;
}

input {
  flex: 1;
  font-size: 28rpx;
  padding: 12rpx 0;
  color: var(--text-color);
}

.region-input {
  flex: 1;
  font-size: 28rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
}

.arrow {
  display: flex;
  align-items: center;
}

.icon-arrow {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

.placeholder {
  color: var(--text-color-light);
}

.switch {
  justify-content: space-between;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx 48rpx;
  background: #fff;
  z-index: 50;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.save-btn {
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
  color: #fff;
  border-radius: 44rpx;
  font-size: 30rpx;
  height: 88rpx;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  letter-spacing: 2rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 16rpx rgba(139, 69, 19, 0.2);
  border: none;
  padding: 0;
}

.save-btn::after {
  border: none;
}

.region-select-btn {
  width: 100%;
  background-color: var(--primary-color);
  color: #fff;
  font-size: 28rpx;
  padding: 15rpx 0;
  border-radius: 8rpx;
  text-align: center;
  margin: 0;
}

.region-btn-item {
  margin-top: -10rpx;
  border-top: none;
  padding-top: 0;
}

.region-btn-item .label {
  width: 160rpx;
  visibility: hidden;
}

/* 地区选择器 */
.region-selector-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  transition: all 0.3s ease;
}

.region-selector {
  width: 100%;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.region-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 32rpx;
  font-weight: 500;
}

.close-btn {
  color: var(--text-color-light);
  font-size: 28rpx;
  padding: 10rpx 20rpx;
}

.region-tabs {
  display: flex;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  overflow-x: auto;
  white-space: nowrap;
}

.tab {
  font-size: 28rpx;
  color: var(--text-color-light);
  margin-right: 40rpx;
  position: relative;
  padding: 12rpx 0;
}

.tab.active {
  color: var(--primary-color);
  font-weight: bold;
}

.tab.active:after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: 0;
  width: 40rpx;
  height: 4rpx;
  background-color: var(--primary-color);
  border-radius: 2rpx;
  transform: translateX(-50%);
}

.region-content {
  flex: 1;
  overflow-y: auto;
  max-height: 60vh;
}

.region-list {
  display: flex;
  flex-wrap: wrap;
  padding: 16rpx;
}

.region-item {
  width: 33.33%;
  padding: 24rpx 0;
  text-align: center;
  font-size: 28rpx;
  box-sizing: border-box;
  color: var(--text-color);
}

.region-item.active {
  color: var(--primary-color);
  font-weight: bold;
  position: relative;
}

.region-item.active::after {
  content: '';
  position: absolute;
  bottom: 10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 16rpx;
  height: 16rpx;
  background-color: var(--primary-color);
  border-radius: 50%;
  opacity: 0.2;
}

.region-footer {
  padding: 24rpx 32rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
}

.region-footer button {
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  height: 88rpx;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: 500;
  letter-spacing: 2rpx;
  box-shadow: 0 8rpx 16rpx rgba(139, 69, 19, 0.2);
  border: none;
  padding: 0;
}

.region-footer button::after {
  border: none;
} 