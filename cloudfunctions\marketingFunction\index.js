// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command
const $ = db.command.aggregate

// 优惠券状态枚举
const CouponStatus = {
  VALID: 0,      // 有效
  USED: 1,       // 已使用
  EXPIRED: 2,    // 已过期
  DISABLED: 3    // 已禁用
}

// 优惠券类型枚举
const CouponType = {
  AMOUNT: 0,     // 满减券
  DISCOUNT: 1,   // 折扣券
  UNLIMITED: 2   // 无门槛券
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  console.log(`执行营销功能: ${action}`, data)

  // 根据action参数执行不同操作
  switch (action) {
    // 优惠券相关
    case 'createCoupon':
      return await createCoupon(data)
    case 'getCouponList':
      return await getCouponList(data, openid)
    case 'exchangeCoupon':
      return await exchangeCoupon(data, openid)
    case 'useCoupon':
      return await useCoupon(data, openid)
    case 'checkCouponValidity':
      return await checkCouponValidity(data, openid)
    case 'getCoupons':
      return await getCoupons(openid)
    case 'receiveCoupon':
      return await receiveCoupon(data, openid)
    case 'getMyCoupons':
      return await getMyCoupons(data, openid)
    case 'checkCoupon':
      return await checkCoupon(data, openid)
    // 促销活动相关
    case 'createPromotion':
      return await createPromotion(data)
    case 'getPromotionList':
      return await getPromotionList(data)
    case 'updatePromotionStatus':
      return await updatePromotionStatus(data)
    default:
      return {
        code: -1,
        msg: '未知的操作类型'
      }
  }
}

// 创建优惠券
async function createCoupon(data) {
  try {
    const {
      name,
      type,
      value,
      minAmount = 0,
      startTime,
      endTime,
      totalCount,
      description = '',
      exchangeCode = ''
    } = data

    const couponData = {
      name,
      type,
      value,
      minAmount,
      startTime: new Date(startTime),
      endTime: new Date(endTime),
      totalCount,
      remainingCount: totalCount,
      description,
      exchangeCode,
      status: CouponStatus.VALID,
      createTime: db.serverDate(),
      updateTime: db.serverDate()
    }

    const result = await db.collection('coupons').add({
      data: couponData
    })

    return {
      code: 0,
      msg: '创建成功',
      data: {
        couponId: result._id
      }
    }
  } catch (error) {
    return {
      code: -1,
      msg: '创建失败',
      error
    }
  }
}

// 获取优惠券列表
async function getCouponList(data, openid) {
  try {
    const { status, page = 1, pageSize = 10 } = data

    // 获取用户的优惠券
    const userCoupons = await db.collection('userCoupons')
      .aggregate()
      .match({
        _openid: openid,
        ...(status !== undefined ? { status } : {})
      })
      .lookup({
        from: 'coupons',
        localField: 'couponId',
        foreignField: '_id',
        as: 'couponInfo'
      })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .end()

    return {
      code: 0,
      msg: '获取成功',
      data: userCoupons.list
    }
  } catch (error) {
    return {
      code: -1,
      msg: '获取失败',
      error
    }
  }
}

// 兑换优惠券
async function exchangeCoupon(data, openid) {
  try {
    const { exchangeCode } = data

    // 查找优惠券
    const coupon = await db.collection('coupons')
      .where({
        exchangeCode,
        status: CouponStatus.VALID,
        remainingCount: _.gt(0)
      })
      .get()

    if (!coupon.data.length) {
      return {
        code: -1,
        msg: '优惠券不存在或已被领完'
      }
    }

    // 检查是否已领取过
    const hasExchanged = await db.collection('userCoupons')
      .where({
        _openid: openid,
        couponId: coupon.data[0]._id
      })
      .count()

    if (hasExchanged.total > 0) {
      return {
        code: -1,
        msg: '您已领取过该优惠券'
      }
    }

    // 添加用户优惠券记录
    await db.collection('userCoupons').add({
      data: {
        _openid: openid,
        couponId: coupon.data[0]._id,
        status: CouponStatus.VALID,
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      }
    })

    // 更新优惠券剩余数量
    await db.collection('coupons').doc(coupon.data[0]._id).update({
      data: {
        remainingCount: _.inc(-1)
      }
    })

    return {
      code: 0,
      msg: '兑换成功'
    }
  } catch (error) {
    return {
      code: -1,
      msg: '兑换失败',
      error
    }
  }
}

// 使用优惠券
async function useCoupon(data, openid) {
  try {
    const { userCouponId, orderId } = data

    // 查询用户优惠券
    const userCouponRes = await db.collection('userCoupons').doc(userCouponId).get()
    if (!userCouponRes.data) {
      return {
        code: -1,
        msg: '优惠券不存在'
      }
    }

    const userCoupon = userCouponRes.data

    // 检查优惠券所有者
    if (userCoupon._openid !== openid) {
      return {
        code: -1,
        msg: '无权使用此优惠券'
      }
    }

    // 检查优惠券状态
    if (userCoupon.status !== 0) {
      return {
        code: -1,
        msg: '优惠券已使用或已过期'
      }
    }

    // 检查优惠券有效期
    const now = new Date()
    if (now < new Date(userCoupon.startTime) || now > new Date(userCoupon.endTime)) {
      return {
        code: -1,
        msg: '优惠券不在有效期内'
      }
    }

    // 更新优惠券状态
    await db.collection('userCoupons').doc(userCouponId).update({
      data: {
        status: 1,
        useTime: db.serverDate(),
        orderId,
        updateTime: db.serverDate()
      }
    })

    return {
      code: 0,
      msg: '使用优惠券成功'
    }
  } catch (error) {
    console.error('使用优惠券失败:', error)
    return {
      code: -1,
      msg: '使用优惠券失败',
      error: error.message
    }
  }
}

// 检查优惠券有效性
async function checkCouponValidity(data, openid) {
  try {
    const { userCouponId, orderAmount } = data

    // 获取用户优惠券信息
    const userCoupon = await db.collection('userCoupons')
      .aggregate()
      .match({
        _id: userCouponId,
        _openid: openid,
        status: CouponStatus.VALID
      })
      .lookup({
        from: 'coupons',
        localField: 'couponId',
        foreignField: '_id',
        as: 'couponInfo'
      })
      .end()

    if (!userCoupon.list.length) {
      return {
        code: -1,
        msg: '优惠券不存在或已使用'
      }
    }

    const couponInfo = userCoupon.list[0].couponInfo[0]
    const now = new Date()

    // 检查有效期
    if (now < couponInfo.startTime || now > couponInfo.endTime) {
      return {
        code: -1,
        msg: '优惠券不在有效期内'
      }
    }

    // 检查使用门槛
    if (orderAmount < couponInfo.minAmount) {
      return {
        code: -1,
        msg: `订单金额未满${couponInfo.minAmount}元`
      }
    }

    return {
      code: 0,
      msg: '优惠券可用',
      data: couponInfo
    }
  } catch (error) {
    return {
      code: -1,
      msg: '检查失败',
      error
    }
  }
}

// 获取可领取的优惠券列表
async function getCoupons(openid) {
  try {
    // 检查优惠券集合是否存在
    try {
      await db.createCollection('coupons')
      console.log('创建coupons集合成功')
    } catch (err) {
      console.log('coupons集合已存在')
    }
    
    // 查询可用的优惠券
    const couponsRes = await db.collection('coupons')
      .where({
        status: 1, // 1表示可用
        endTime: _.gt(db.serverDate()),
        startTime: _.lte(db.serverDate())
      })
      .get()

    // 查询用户已领取的优惠券
    const userCouponsRes = await db.collection('userCoupons')
      .where({
        _openid: openid
      })
      .get()

    // 标记已领取的优惠券
    const userCouponIds = userCouponsRes.data.map(uc => uc.couponId)
    const coupons = couponsRes.data.map(coupon => {
      return {
        ...coupon,
        isReceived: userCouponIds.includes(coupon._id)
      }
    })

    return {
      code: 0,
      msg: '获取优惠券列表成功',
      data: coupons
    }
  } catch (error) {
    console.error('获取优惠券列表失败:', error)
    return {
      code: -1,
      msg: '获取优惠券列表失败',
      error: error.message
    }
  }
}

// 领取优惠券
async function receiveCoupon(data, openid) {
  try {
    // 检查用户优惠券集合是否存在
    try {
      await db.createCollection('userCoupons')
      console.log('创建userCoupons集合成功')
    } catch (err) {
      console.log('userCoupons集合已存在')
    }
    
    const { couponId } = data

    // 查询优惠券信息
    const couponRes = await db.collection('coupons').doc(couponId).get()
    if (!couponRes.data) {
      return {
        code: -1,
        msg: '优惠券不存在'
      }
    }

    const coupon = couponRes.data

    // 检查优惠券是否可用
    if (coupon.status !== 1) {
      return {
        code: -1,
        msg: '优惠券已下架'
      }
    }

    const now = new Date()
    if (now < new Date(coupon.startTime) || now > new Date(coupon.endTime)) {
      return {
        code: -1,
        msg: '优惠券不在有效期内'
      }
    }

    // 检查用户是否已领取过该优惠券
    const userCouponRes = await db.collection('userCoupons')
      .where({
        couponId,
        _openid: openid
      })
      .get()

    if (userCouponRes.data && userCouponRes.data.length > 0) {
      return {
        code: -1,
        msg: '您已领取过该优惠券'
      }
    }

    // 检查优惠券剩余数量
    if (coupon.remainingQuantity <= 0) {
      return {
        code: -1,
        msg: '优惠券已领完'
      }
    }

    // 领取优惠券
    await db.collection('userCoupons').add({
      data: {
        couponId,
        name: coupon.name,
        type: coupon.type,
        value: coupon.value,
        minAmount: coupon.minAmount,
        startTime: coupon.startTime,
        endTime: coupon.endTime,
        status: 0, // 0:未使用, 1:已使用, -1:已过期
        _openid: openid,
        createTime: db.serverDate(),
        useTime: null
      }
    })

    // 更新优惠券剩余数量
    await db.collection('coupons').doc(couponId).update({
      data: {
        remainingQuantity: _.inc(-1),
        updateTime: db.serverDate()
      }
    })

    return {
      code: 0,
      msg: '领取优惠券成功'
    }
  } catch (error) {
    console.error('领取优惠券失败:', error)
    return {
      code: -1,
      msg: '领取优惠券失败',
      error: error.message
    }
  }
}

// 获取我的优惠券
async function getMyCoupons(data, openid) {
  try {
    const { status, page = 1, pageSize = 10 } = data
    const skip = (page - 1) * pageSize

    // 构建查询条件
    const condition = { _openid: openid }
    if (status !== undefined && status !== null) {
      condition.status = status
    }

    // 更新已过期优惠券
    const now = db.serverDate()
    await db.collection('userCoupons')
      .where({
        _openid: openid,
        status: 0,
        endTime: _.lt(now)
      })
      .update({
        data: {
          status: -1,
          updateTime: db.serverDate()
        }
      })

    // 查询优惠券
    const couponsRes = await db.collection('userCoupons')
      .where(condition)
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get()

    // 获取总数
    const countRes = await db.collection('userCoupons')
      .where(condition)
      .count()

    return {
      code: 0,
      msg: '获取我的优惠券成功',
      data: {
        total: countRes.total,
        list: couponsRes.data,
        page,
        pageSize
      }
    }
  } catch (error) {
    console.error('获取我的优惠券失败:', error)
    return {
      code: -1,
      msg: '获取我的优惠券失败',
      error: error.message
    }
  }
}

// 检查优惠券是否可用
async function checkCoupon(data, openid) {
  try {
    const { userCouponId, totalAmount } = data

    // 查询用户优惠券
    const userCouponRes = await db.collection('userCoupons').doc(userCouponId).get()
    if (!userCouponRes.data) {
      return {
        code: -1,
        msg: '优惠券不存在'
      }
    }

    const userCoupon = userCouponRes.data

    // 检查优惠券所有者
    if (userCoupon._openid !== openid) {
      return {
        code: -1,
        msg: '无权使用此优惠券'
      }
    }

    // 检查优惠券状态
    if (userCoupon.status !== 0) {
      return {
        code: -1,
        msg: '优惠券已使用或已过期'
      }
    }

    // 检查优惠券有效期
    const now = new Date()
    if (now < new Date(userCoupon.startTime) || now > new Date(userCoupon.endTime)) {
      return {
        code: -1,
        msg: '优惠券不在有效期内'
      }
    }

    // 检查订单金额是否满足优惠券要求
    if (totalAmount < userCoupon.minAmount) {
      return {
        code: -1,
        msg: `订单金额不满${userCoupon.minAmount}元`
      }
    }

    // 计算优惠金额
    let discountAmount = 0
    if (userCoupon.type === 0) {
      // 满减券
      discountAmount = userCoupon.value
    } else if (userCoupon.type === 1) {
      // 折扣券，value是折扣比例，如80表示8折
      discountAmount = totalAmount * (1 - userCoupon.value / 100)
      discountAmount = Math.round(discountAmount * 100) / 100 // 四舍五入到2位小数
    }

    return {
      code: 0,
      msg: '优惠券可用',
      data: {
        coupon: userCoupon,
        discountAmount
      }
    }
  } catch (error) {
    console.error('检查优惠券失败:', error)
    return {
      code: -1,
      msg: '检查优惠券失败',
      error: error.message
    }
  }
}

// 创建促销活动
async function createPromotion(data) {
  try {
    const {
      name,
      type,
      rules,
      startTime,
      endTime,
      products = [],
      description = ''
    } = data

    const promotionData = {
      name,
      type,
      rules,
      startTime: new Date(startTime),
      endTime: new Date(endTime),
      products,
      description,
      status: 1, // 1: 启用
      createTime: db.serverDate(),
      updateTime: db.serverDate()
    }

    const result = await db.collection('promotions').add({
      data: promotionData
    })

    return {
      code: 0,
      msg: '创建成功',
      data: {
        promotionId: result._id
      }
    }
  } catch (error) {
    return {
      code: -1,
      msg: '创建失败',
      error
    }
  }
}

// 获取促销活动列表
async function getPromotionList(data) {
  try {
    const { status, page = 1, pageSize = 10 } = data

    const query = status !== undefined ? { status } : {}
    
    const total = await db.collection('promotions')
      .where(query)
      .count()

    const promotions = await db.collection('promotions')
      .where(query)
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get()

    return {
      code: 0,
      msg: '获取成功',
      data: {
        total: total.total,
        list: promotions.data
      }
    }
  } catch (error) {
    return {
      code: -1,
      msg: '获取失败',
      error
    }
  }
}

// 更新促销活动状态
async function updatePromotionStatus(data) {
  try {
    const { promotionId, status } = data

    await db.collection('promotions').doc(promotionId).update({
      data: {
        status,
        updateTime: db.serverDate()
      }
    })

    return {
      code: 0,
      msg: '更新成功'
    }
  } catch (error) {
    return {
      code: -1,
      msg: '更新失败',
      error
    }
  }
} 