// 引入环境配置文件，就像拿到了家里的钥匙和地址
const ENV = require('../../config/env');

Page({
  data: {
    loading: true,   // 加载状态，就像转圈圈的等待图标
    // 轮播图相关数据
    banners: [],           // 轮播图数组，就像相册里的照片集合
    isCloudAvailable: true, // 云服务是否可用，就像检查网络信号是否满格
    networkStatus: 'unknown', // 网络状态，就像手机显示的WiFi/4G标识 - 注意这里加了逗号
    // 新增：屏幕尺寸数据
    screenHeight: 0,
    screenWidth: 0,
    statusBarHeight: 0,
    navigationBarHeight: 0,
    tabBarHeight: 0,
    availableHeight: 0
  },

  onLoad() {
    console.log('首页加载'); // 在控制台打印日志，就像写日记记录发生了什么
    this.getSystemInfo(); // 先获取系统信息
    this.checkNetworkAndLoadData(); // 检查网络然后加载数据，就像出门前先看天气预报
  },

  // 新增：获取系统信息，自适应屏幕尺寸
  getSystemInfo() {
    const systemInfo = wx.getSystemInfoSync();
    console.log('[系统信息]', systemInfo);
    
    // 获取各种高度
    const statusBarHeight = systemInfo.statusBarHeight || 0;
    const navigationBarHeight = 44; // 导航栏标准高度
    const safeAreaBottom = systemInfo.safeArea ? (systemInfo.screenHeight - systemInfo.safeArea.bottom) : 0;
    const tabBarHeight = 48 + safeAreaBottom;
    
    // 轮播图高度 = 屏幕总高度 - 状态栏 - 导航栏 - tabBar高度
    const bannerHeight = systemInfo.screenHeight - statusBarHeight - navigationBarHeight - tabBarHeight;
    const topOffset = statusBarHeight + navigationBarHeight; // 顶部偏移量
    
    this.setData({
      screenHeight: systemInfo.screenHeight,
      screenWidth: systemInfo.screenWidth,
      statusBarHeight: statusBarHeight,
      navigationBarHeight: navigationBarHeight,
      tabBarHeight: tabBarHeight,
      safeAreaBottom: safeAreaBottom,
      bannerHeight: bannerHeight,
      topOffset: topOffset // 新增：顶部偏移量
    });
    
    console.log('[屏幕适配] 状态栏高度:', statusBarHeight);
    console.log('[屏幕适配] 导航栏高度:', navigationBarHeight);
    console.log('[屏幕适配] 顶部偏移:', topOffset);
    console.log('[屏幕适配] 轮播图高度:', bannerHeight);
  },

  // 检查网络状态和云服务可用性
  async checkNetworkAndLoadData() {
    console.log('[首页] 开始检查网络状态'); // 记录开始检查网络，就像说"我要开始测网速了"
    
    try {
      // 第一步：检查网络连接状态
      const networkInfo = await this.getNetworkStatus(); // 获取网络信息，就像查看手机信号格数
      console.log('[首页] 网络状态:', networkInfo.networkType); // 打印网络类型，就像说"我现在用的是WiFi"
      
      // 第二步：检查云服务是否能连通
      const cloudStatus = await this.checkCloudService(); // 检查云服务，就像测试能否连接到公司服务器
      console.log('[首页] 云服务状态:', cloudStatus ? '可用' : '不可用'); // 记录云服务状态
      
      // 第三步：更新页面数据状态
      this.setData({
        networkStatus: networkInfo.networkType, // 保存网络类型，就像记住现在用的是WiFi还是流量
        isCloudAvailable: cloudStatus           // 保存云服务状态，就像记住服务器是否正常
      });
      
      // 第四步：根据检查结果加载轮播图数据
      this.loadBannerData(); // 开始加载轮播图，就像确认网络正常后开始下载图片
      
    } catch (error) {
      console.error('[首页] 网络检查失败:', error); // 记录错误，就像写"网络检查出问题了"
      // 如果检查失败，默认使用降级方案
      this.setData({
        isCloudAvailable: false, // 标记云服务不可用，就像标记"服务器连不上"
        networkStatus: 'none'    // 标记无网络，就像手机显示"无信号"
      });
      this.loadBannerData(); // 即使网络有问题也要加载轮播图，就像没网也要看本地缓存的图片
    }
  },

  // 获取网络状态信息
  getNetworkStatus() {
    return new Promise((resolve, reject) => { // 创建一个承诺，就像说"我保证会给你答案"
      wx.getNetworkType({ // 调用微信API获取网络类型，就像问手机"现在用的什么网络？"
        success: (res) => { // 成功获取到网络信息时执行，就像手机回答"我用的是WiFi"
          console.log('[网络检测] 网络类型:', res.networkType); // 记录网络类型
          resolve(res); // 返回成功结果，就像说"任务完成，这是答案"
        },
        fail: (err) => { // 获取失败时执行，就像手机说"我也不知道用的什么网络"
          console.error('[网络检测] 获取网络状态失败:', err); // 记录错误信息
          reject(err); // 返回失败结果，就像说"抱歉，任务失败了"
        }
      });
    });
  },

  // 检查云服务是否可用
  async checkCloudService() {
    try {
      console.log('[云服务检测] 开始检测云环境连接'); // 记录开始检测，就像说"开始测试能否连接服务器"
      
      // 尝试调用云数据库，就像敲门看有没有人回应
      const db = wx.cloud.database(); // 获取数据库连接，就像拿到了数据库的钥匙
      
      // 设置超时时间为5秒，就像说"如果5秒内没回应就算连不上"
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('连接超时')), 5000); // 5秒后自动失败
      });
      
      // 尝试获取一条轮播图数据来测试连接，就像发个"hello"看服务器是否回应
      const testPromise = db.collection('banners').limit(1).get();
      
      // 等待测试结果或超时，就像等电话接通或忙音
      await Promise.race([testPromise, timeoutPromise]);
      
      console.log('[云服务检测] 云服务连接正常'); // 记录成功，就像说"服务器连接正常"
      return true; // 返回成功，就像竖起大拇指
      
    } catch (error) {
      console.error('[云服务检测] 云服务连接失败:', error.message); // 记录失败原因
      return false; // 返回失败，就像摇头说"连不上"
    }
  },

  // 新增：加载轮播图数据的主函数
  async loadBannerData() {
    console.log('[轮播图] 开始加载轮播图数据'); // 记录开始加载，就像说"开始准备相册"
    
    // 如果云服务可用，尝试从云数据库获取轮播图
    if (this.data.isCloudAvailable) {
      console.log('[轮播图] 云服务可用，从数据库获取轮播图'); // 就像说"网络正常，去服务器下载图片"
      await this.loadCloudBanners(); // 从云端获取轮播图，就像从网上下载最新的照片
    } else {
      console.log('[轮播图] 云服务不可用，使用本地默认轮播图'); // 就像说"网络不好，用本地存储的图片"
      this.loadLocalBanners(); // 使用本地轮播图，就像打开手机相册看之前保存的照片
    }
    
    this.setData({
      loading: false // 取消加载状态，就像关闭转圈圈的等待图标
    });
  },

  // 新增：从云数据库获取轮播图
  async loadCloudBanners() {
    try {
      console.log('[云轮播图] 开始从数据库获取轮播图'); // 就像说"开始从服务器下载图片"
      
      const db = wx.cloud.database(); // 获取数据库连接，就像拿到图书馆的借书证
      
      // 设置查询超时时间，就像说"如果10秒内没下载完就放弃"
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('获取轮播图超时')), 10000); // 10秒超时
      });
      
      // 查询轮播图数据，按排序字段升序排列
      const queryPromise = db.collection(ENV.COLLECTIONS.BANNERS) // 从轮播图表格中查询，就像翻开相册的某一页
        .where({ isActive: true }) // 只获取启用的轮播图，就像只看没有被删除的照片
        .orderBy('sort', 'asc') // 按排序字段升序排列，就像按拍照时间排序
        .limit(ENV.PAGE_SIZE.BANNERS || 5) // 限制获取数量，就像说"最多拿5张照片"
        .get(); // 执行查询，就像按下"搜索"按钮
      
      // 等待查询结果或超时
      const result = await Promise.race([queryPromise, timeoutPromise]);
      
      console.log('[云轮播图] 数据库查询成功，获取到', result.data.length, '张轮播图'); // 记录成功获取的数量
      
      if (result.data && result.data.length > 0) {
        // 处理轮播图数据，就像整理刚下载的照片
        const banners = await this.processBannerData(result.data);
        console.log('[云轮播图] 轮播图数据处理完成'); // 就像说"照片整理好了"
        
        this.setData({
          banners: banners // 更新轮播图数据，就像把整理好的照片放到相册里
        });
      } else {
        console.log('[云轮播图] 数据库中没有轮播图数据，使用本地默认图片'); // 就像说"服务器上没有图片，用本地的"
        this.loadLocalBanners(); // 降级到本地轮播图，就像打开本地相册
      }
      
    } catch (error) {
      console.error('[云轮播图] 获取云轮播图失败:', error.message); // 记录失败原因
      console.log('[云轮播图] 降级使用本地轮播图'); // 就像说"下载失败了，用本地图片吧"
      this.loadLocalBanners(); // 降级到本地轮播图，就像网络不好时看缓存的图片
    }
  },

  // 新增：处理轮播图数据（获取云存储图片URL）
  async processBannerData(rawBanners) {
    console.log('[轮播图处理] 开始处理', rawBanners.length, '张轮播图'); // 就像说"开始处理这些照片"
    
    const processedBanners = []; // 创建处理后的轮播图数组，就像准备一个新相册
    
    // 遍历每张轮播图进行处理
    for (let i = 0; i < rawBanners.length; i++) {
      const banner = rawBanners[i]; // 获取当前轮播图，就像拿起一张照片
      console.log(`[轮播图处理] 处理第${i + 1}张轮播图:`, banner.title || '无标题'); // 记录正在处理的图片
      
      try {
        let imageUrl = ''; // 图片URL，就像照片的网址
        
        if (banner.imageUrl) {
          // 如果有云存储路径，获取临时访问URL
          console.log(`[轮播图处理] 获取云存储图片URL:`, banner.imageUrl); // 就像说"获取这张照片的网址"
          imageUrl = await this.getCloudImageUrl(banner.imageUrl); // 获取云存储图片的访问地址
        } else {
          // 如果没有云存储路径，使用默认图片
          console.log(`[轮播图处理] 使用默认轮播图`); // 就像说"这张照片坏了，用默认的"
          imageUrl = ENV.DEFAULT_IMAGES.BANNER; // 使用默认轮播图，就像用备用照片
        }
        
        // 构建处理后的轮播图对象
        const processedBanner = {
          id: banner._id, // 轮播图ID，就像照片的编号
          title: banner.title || '轮播图', // 标题，就像照片的名字
          imageUrl: imageUrl, // 图片地址，就像照片的存放位置
          linkUrl: banner.linkUrl || '', // 点击跳转链接，就像照片背后的故事链接
          sort: banner.sort || i // 排序，就像照片在相册中的位置
        };
        
        processedBanners.push(processedBanner); // 添加到处理后的数组，就像把照片放进新相册
        console.log(`[轮播图处理] 第${i + 1}张轮播图处理完成`); // 记录处理完成
        
      } catch (error) {
        console.error(`[轮播图处理] 第${i + 1}张轮播图处理失败:`, error.message); // 记录处理失败
        // 处理失败时使用默认图片，就像照片坏了用备用的
        processedBanners.push({
          id: banner._id || `default_${i}`,
          title: banner.title || '轮播图',
          imageUrl: ENV.DEFAULT_IMAGES.BANNER, // 使用默认图片
          linkUrl: banner.linkUrl || '',
          sort: banner.sort || i
        });
      }
    }
    
    console.log('[轮播图处理] 所有轮播图处理完成，共', processedBanners.length, '张'); // 记录处理完成的总数
    return processedBanners; // 返回处理后的轮播图数组，就像交出整理好的相册
  },

  // 新增：获取云存储图片的临时访问URL
  async getCloudImageUrl(cloudPath) {
    try {
      console.log('[云存储] 获取图片临时URL:', cloudPath);
      
      // 调用云存储API获取临时访问链接
      const result = await wx.cloud.getTempFileURL({
        fileList: [cloudPath]
      });
      
      if (result.fileList && result.fileList.length > 0 && result.fileList[0].tempFileURL) {
        const tempUrl = result.fileList[0].tempFileURL;
        console.log('[云存储] 获取临时URL成功');
        return tempUrl;
      } else {
        throw new Error('获取临时URL失败');
      }
      
    } catch (error) {
      console.error('[云存储] 获取云存储图片URL失败:', error.message);
      // 直接返回ENV中配置的默认轮播图
      return ENV.DEFAULT_IMAGES.BANNER;
    }
  },

  // 改进：加载本地默认轮播图（使用真实的轮播图片）
  loadLocalBanners(reason = 'unknown') {
    console.log('[本地轮播图] 启用本地降级方案，原因:', reason);
    
    // 使用真实的轮播图片
    const bannerImages = ENV.DEFAULT_IMAGES.BANNER_LIST || [ENV.DEFAULT_IMAGES.BANNER];
    const localBanners = bannerImages.map((imageUrl, index) => ({
      id: `local_banner_${index + 1}`,
      title: `轮播图${index + 1}`,
      imageUrl: imageUrl,
      linkUrl: '',
      sort: index + 1,
      isLocal: true,
      fallbackReason: reason
    }));
    
    console.log('[本地轮播图] 使用本地轮播图片:', bannerImages);
    
    this.setData({
      banners: localBanners,
      isUsingLocalBanners: true,
      bannerLoadError: reason === 'load_failed'
    });
  },

  // 轮播图图片加载错误处理（使用ENV配置）
  onBannerImageError(e) {
    const index = e.currentTarget.dataset.index;
    console.log('[轮播图] 图片加载失败，索引:', index, '使用ENV默认图片');
    
    const banners = this.data.banners;
    
    if (banners[index]) {
      // 直接使用ENV配置的默认轮播图
      banners[index].imageUrl = ENV.DEFAULT_IMAGES.BANNER;
      this.setData({ banners });
      
      console.log('[轮播图] 已替换为ENV配置的默认图片:', ENV.DEFAULT_IMAGES.BANNER);
    }
  }
});














