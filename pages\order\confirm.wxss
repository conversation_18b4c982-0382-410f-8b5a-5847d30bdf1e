/* pages/order/confirm.wxss */
.container {
  padding-bottom: 120rpx;
  background-color: var(--background-color);
}

/* 地址部分 */
.address-section {
  display: flex;
  background-color: #fff;
  padding: 30rpx;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.address-section::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 4rpx;
  background: linear-gradient(to right, rgba(var(--primary-color-rgb), 0.7), rgba(var(--secondary-color-rgb), 0.7));
}

.address-info {
  flex: 1;
}

.contact {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.name {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-right: 20rpx;
}

.phone {
  color: var(--text-color-light);
}

.address {
  font-size: 28rpx;
  color: var(--text-color-light);
  line-height: 1.4;
}

.no-address {
  display: flex;
  align-items: center;
  color: rgba(var(--primary-color-rgb), 0.9);
  font-size: 30rpx;
}

.no-address::before {
  content: '+';
  margin-right: 10rpx;
  font-weight: bold;
}

.arrow {
  display: flex;
  align-items: center;
  color: #ccc;
  margin-left: 20rpx;
}

.arrow image {
  width: 16rpx;
  height: 28rpx;
}

/* 商品列表 */
.product-list {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.product-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  flex-shrink: 0;
  overflow: hidden;
}

.product-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  color: var(--text-color);
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-spec {
  font-size: 24rpx;
  color: var(--text-color-light);
  margin-bottom: 10rpx;
  background-color: #f5f5f5;
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.price-quantity {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-size: 32rpx;
  color: #E84C3D;
  font-weight: bold;
}

.price::before {
  content: '¥';
  font-size: 24rpx;
  color: #E84C3D;
  margin-right: 2rpx;
}

.quantity {
  color: var(--text-color-light);
  font-size: 26rpx;
}

/* 优惠券部分 */
.coupon-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.label {
  font-size: 28rpx;
  color: var(--text-color);
}

.coupon-info {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-right: 20rpx;
}

.coupon-value {
  color: rgba(var(--secondary-color-rgb), 0.9);
  font-size: 28rpx;
}

.no-coupon {
  color: var(--text-color-light);
  font-size: 28rpx;
}

/* 优惠选择样式 */
.coupon-select {
  padding: 10rpx 0;
}

.discount-value {
  color: #E84C3D;
}

.no-discount {
  color: var(--text-color-light);
  font-size: 26rpx;
}

.coupon-info .arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 6rpx;
}

/* 订单金额 */
.order-amount {
  background-color: #fff;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.amount-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: var(--text-color);
}

.amount-item:last-child {
  margin-bottom: 0;
}

.total {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1px solid #f5f5f5;
}

.discount {
  color: #E84C3D;
}

.final-price {
  color: #E84C3D;
  font-size: 34rpx;
  font-weight: bold;
}

.final-price::before {
  content: '¥';
  font-size: 24rpx;
  color: #E84C3D;
  margin-right: 2rpx;
}

/* 备注 */
.remark-section {
  background-color: #fff;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.remark-input {
  width: 100%;
  height: 80rpx;
  font-size: 28rpx;
  color: var(--text-color);
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 0 20rpx;
}

/* 底部提交栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}

.total-section {
  flex: 1;
  display: flex;
  align-items: center;
}

.submit-btn {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: linear-gradient(to right, rgba(var(--primary-color-rgb), 0.9), rgba(var(--secondary-color-rgb), 0.9));
  color: #fff;
  font-size: 30rpx;
  border-radius: 40rpx;
  margin-left: 30rpx;
  transition: opacity 0.3s;
  font-weight: bold;
}

.submit-btn:active {
  opacity: 0.8;
}

.submit-btn[disabled] {
  background: #cccccc;
  color: #ffffff;
  opacity: 0.7;
}