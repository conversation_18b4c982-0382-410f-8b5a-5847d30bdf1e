{"permission": {"read": "auth != null", "write": "auth != null", "create": "auth != null && ((!env.isProduction && auth.userId == request.data.userId) || (env.isProduction && auth.openid == request.data._openid))", "update": "auth != null && ((!env.isProduction && auth.userId == resource.data.userId) || (env.isProduction && auth.openid == resource.data._openid))", "delete": "auth != null && ((!env.isProduction && auth.userId == resource.data.userId) || (env.isProduction && auth.openid == resource.data._openid))"}}