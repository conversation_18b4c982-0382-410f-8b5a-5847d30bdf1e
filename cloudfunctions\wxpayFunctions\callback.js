// 云函数入口文件
const cloud = require('wx-server-sdk');
const crypto = require('crypto');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 支付配置
const payConfig = {
  // 微信支付APIv3密钥（用于解密回调数据，需要与支付配置一致）
  apiv3Key: process.env.APIV3_KEY || 'uwsbakfkbalskhdlhalkfhlahslfhlah'
};

/**
 * 解密回调数据
 * @param {String} ciphertext 加密数据
 * @param {String} nonce 随机字符串
 * @param {String} associated_data 附加数据
 * @returns {String} 解密后的数据
 */
function decryptData(ciphertext, nonce, associated_data) {
  try {
    // base64解码密文
    const decodedCiphertext = Buffer.from(ciphertext, 'base64');
    
    // 构造认证数据
    const authTag = decodedCiphertext.slice(decodedCiphertext.length - 16);
    const ciphertextWithoutAuthTag = decodedCiphertext.slice(0, decodedCiphertext.length - 16);
    
    // 使用AEAD_AES_256_GCM算法解密
    const decipher = crypto.createDecipheriv(
      'aes-256-gcm',
      payConfig.apiv3Key,
      Buffer.from(nonce, 'utf8')
    );
    
    decipher.setAuthTag(authTag);
    decipher.setAAD(Buffer.from(associated_data, 'utf8'));
    
    // 解密
    const decryptedData = decipher.update(ciphertextWithoutAuthTag, null, 'utf8');
    const final = decipher.final('utf8');
    
    return decryptedData + final;
  } catch (err) {
    console.error('[支付回调] 解密数据失败:', err);
    throw new Error('解密数据失败: ' + err.message);
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    console.log('[支付回调] 收到回调数据:', event);
    
    // 获取微信的请求头
    const headers = event.headers || {};
    const wechatpaySignature = headers['Wechatpay-Signature'];
    const wechatpayTimestamp = headers['Wechatpay-Timestamp'];
    const wechatpayNonce = headers['Wechatpay-Nonce'];
    const wechatpaySerial = headers['Wechatpay-Serial'];
    
    if (!wechatpaySignature || !wechatpayTimestamp || !wechatpayNonce) {
      console.error('[支付回调] 缺少微信支付签名信息');
      return {
        statusCode: 400,
        body: JSON.stringify({
          code: 'FAIL',
          message: '缺少微信支付签名信息'
        })
      };
    }
    
    // 获取请求体
    const requestBody = event.body;
    if (!requestBody) {
      console.error('[支付回调] 缺少请求体');
      return {
        statusCode: 400,
        body: JSON.stringify({
          code: 'FAIL',
          message: '缺少请求体'
        })
      };
    }
    
    console.log('[支付回调] 请求体:', requestBody);
    
    // 解析请求体
    let bodyObj;
    try {
      bodyObj = typeof requestBody === 'string' ? JSON.parse(requestBody) : requestBody;
    } catch (err) {
      console.error('[支付回调] 解析请求体失败:', err);
      return {
        statusCode: 400,
        body: JSON.stringify({
          code: 'FAIL',
          message: '解析请求体失败'
        })
      };
    }
    
    // 解密回调数据
    if (!bodyObj.resource || !bodyObj.resource.ciphertext) {
      console.error('[支付回调] 回调数据格式错误');
      return {
        statusCode: 400,
        body: JSON.stringify({
          code: 'FAIL',
          message: '回调数据格式错误'
        })
      };
    }
    
    const resource = bodyObj.resource;
    let decryptedData;
    
    try {
      decryptedData = decryptData(
        resource.ciphertext,
        resource.nonce,
        resource.associated_data
      );
    } catch (err) {
      console.error('[支付回调] 解密数据失败:', err);
      return {
        statusCode: 400,
        body: JSON.stringify({
          code: 'FAIL',
          message: '解密数据失败: ' + err.message
        })
      };
    }
    
    console.log('[支付回调] 解密后的数据:', decryptedData);
    
    // 解析解密后的数据
    let paymentInfo;
    try {
      paymentInfo = JSON.parse(decryptedData);
    } catch (err) {
      console.error('[支付回调] 解析解密数据失败:', err);
      return {
        statusCode: 400,
        body: JSON.stringify({
          code: 'FAIL',
          message: '解析解密数据失败'
        })
      };
    }
    
    // 提取交易信息
    const {
      mchid,
      appid,
      out_trade_no,
      transaction_id,
      trade_state,
      trade_state_desc,
      bank_type,
      success_time,
      payer,
      amount
    } = paymentInfo;
    
    console.log('[支付回调] 交易信息:', {
      商户号: mchid,
      应用ID: appid,
      商户订单号: out_trade_no,
      微信支付订单号: transaction_id,
      交易状态: trade_state,
      交易状态描述: trade_state_desc,
      银行类型: bank_type,
      支付完成时间: success_time,
      支付者: payer,
      金额: amount
    });
    
    // 验证交易状态
    if (trade_state !== 'SUCCESS') {
      console.log(`[支付回调] 交易未成功，状态: ${trade_state}, 描述: ${trade_state_desc}`);
      return {
        statusCode: 200,
        body: JSON.stringify({
          code: 'SUCCESS',
          message: '收到通知，交易未成功'
        })
      };
    }
    
    // 查询订单信息
    const orderRes = await db.collection('orders').where({
      orderNo: out_trade_no
    }).get();
    
    if (!orderRes.data.length) {
      console.error(`[支付回调] 订单不存在: ${out_trade_no}`);
      return {
        statusCode: 404,
        body: JSON.stringify({
          code: 'FAIL',
          message: `订单不存在: ${out_trade_no}`
        })
      };
    }
    
    const order = orderRes.data[0];
    
    // 验证订单金额
    const totalFee = order.totalFee || Math.floor(order.totalAmount * 100);
    if (totalFee !== amount.total) {
      console.error(`[支付回调] 订单金额不匹配: 订单=${totalFee}, 支付=${amount.total}`);
      return {
        statusCode: 400,
        body: JSON.stringify({
          code: 'FAIL',
          message: `订单金额不匹配`
        })
      };
    }
    
    // 检查订单状态，避免重复处理
    if (order.status === 1 || order.status === '已支付') {
      console.log(`[支付回调] 订单 ${out_trade_no} 已经处理过支付成功状态`);
      return {
        statusCode: 200,
        body: JSON.stringify({
          code: 'SUCCESS',
          message: '订单已处理'
        })
      };
    }
    
    // 更新订单状态
    try {
      await db.collection('orders').doc(order._id).update({
        data: {
          status: 1, // 已支付
          statusText: '已支付',
          payTime: new Date(success_time),
          transactionId: transaction_id,
          paymentInfo: {
            bankType: bank_type,
            openid: payer.openid,
            tradeState: trade_state,
            tradeStateDesc: trade_state_desc
          },
          updateTime: db.serverDate(),
          statusHistory: db.command.push({
            status: 1,
            statusText: '已支付',
            time: new Date(success_time),
            desc: '支付成功'
          })
        }
      });
      
      console.log('[支付回调] 订单处理成功:', {
        订单号: out_trade_no,
        交易号: transaction_id,
        支付时间: success_time
      });
    } catch (err) {
      console.error('[支付回调] 更新订单状态失败:', err);
      return {
        statusCode: 500,
        body: JSON.stringify({
          code: 'FAIL',
          message: '更新订单状态失败: ' + err.message
        })
      };
    }
    
    // 返回成功响应
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        code: 'SUCCESS',
        message: 'OK'
      })
    };
  } catch (err) {
    console.error('[支付回调] 处理失败:', err);
    
    // 返回失败通知
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        code: 'FAIL',
        message: err.message || '处理失败'
      })
    };
  }
}; 