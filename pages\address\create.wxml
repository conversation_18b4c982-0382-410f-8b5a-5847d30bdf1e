<view class="container">
  <form bindsubmit="submitForm">
    <view class="page-title">新增收货地址</view>
    
    <view class="form-group">
      <view class="form-item">
        <text class="label">收货人</text>
        <input name="name" value="{{address.name}}" placeholder="请输入收货人姓名" />
        <view class="line"></view>
      </view>
      <view class="form-item">
        <text class="label">手机号码</text>
        <input name="phone" type="number" value="{{address.phone}}" placeholder="请输入手机号码" />
        <view class="line"></view>
      </view>
      
      <!-- 隐藏的地区选择器 -->
      <view class="form-item region-picker-item" style="display:none;">
        <text class="label">所在地区</text>
        <picker mode="region" bindchange="regionChange" value="{{region}}" id="regionPicker">
          <view class="picker {{region[0] ? '' : 'placeholder'}}" bindtap="onRegionPickerTap">
            {{region[0] ? region[0] + region[1] + region[2] : '请选择所在地区'}}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <!-- 地区选择输入框 -->
      <view class="form-item" bindtap="showRegionSelector">
        <text class="label">所在地区</text>
        <view class="region-input {{!region[0] ? 'placeholder' : ''}}">
          <text>{{region[0] ? region[0] + region[1] + region[2] : '请选择所在地区'}}</text>
          <view class="arrow">
            <image class="icon-arrow" src="/images/icon/arrow-right.png" mode="aspectFit"></image>
          </view>
        </view>
        <view class="line"></view>
      </view>
      
      <view class="form-item">
        <text class="label">详细地址</text>
        <input name="detail" value="{{address.detail}}" placeholder="请输入详细地址" />
      </view>
    </view>

    <view class="form-group">
      <view class="form-item switch">
        <text class="label">设为默认地址</text>
        <switch name="isDefault" checked="{{address.isDefault}}" color="var(--primary-color)" />
      </view>
    </view>

    <view class="footer">
      <button class="save-btn" form-type="submit">
        <text>保存地址</text>
      </button>
    </view>
  </form>
  
  <!-- 地区选择器弹窗 -->
  <view class="region-selector-mask" wx:if="{{showRegionSelector}}" bindtap="hideRegionSelector">
    <view class="region-selector" catchtap="stopPropagation">
      <view class="region-header">
        <text>选择地区</text>
        <text class="close-btn" bindtap="hideRegionSelector">关闭</text>
      </view>
      
      <view class="region-tabs">
        <view class="tab {{provinceIndex >= 0 ? 'active' : ''}}" bindtap="switchToProvince">
          {{provinceIndex >= 0 ? provinces[provinceIndex] : '选择省份'}}
        </view>
        <view class="tab {{cityIndex >= 0 ? 'active' : ''}}" bindtap="switchToCity" wx:if="{{provinceIndex >= 0}}">
          {{cityIndex >= 0 ? cities[cityIndex] : '选择城市'}}
        </view>
        <view class="tab {{districtIndex >= 0 ? 'active' : ''}}" wx:if="{{cityIndex >= 0}}">
          {{districtIndex >= 0 ? districts[districtIndex] : '选择区县'}}
        </view>
      </view>
      
      <view class="region-content">
        <!-- 省份列表 -->
        <view class="region-list" wx:if="{{currentTabIndex === 0 || !currentTabIndex}}">
          <view 
            class="region-item {{provinceIndex === index ? 'active' : ''}}" 
            wx:for="{{provinces}}" 
            wx:key="index" 
            bindtap="selectProvince" 
            data-index="{{index}}">
            {{item}}
          </view>
        </view>
        
        <!-- 城市列表 -->
        <view class="region-list" wx:if="{{currentTabIndex === 1}}">
          <view 
            class="region-item {{cityIndex === index ? 'active' : ''}}" 
            wx:for="{{cities}}" 
            wx:key="index" 
            bindtap="selectCity" 
            data-index="{{index}}">
            {{item}}
          </view>
        </view>
        
        <!-- 区县列表 -->
        <view class="region-list" wx:if="{{currentTabIndex === 2}}">
          <view 
            class="region-item {{districtIndex === index ? 'active' : ''}}" 
            wx:for="{{districts}}" 
            wx:key="index" 
            bindtap="selectDistrict" 
            data-index="{{index}}">
            {{item}}
          </view>
        </view>
      </view>

      <view class="region-footer" wx:if="{{districtIndex >= 0}}">
        <button bindtap="confirmRegion">
          <text>确定</text>
        </button>
      </view>
    </view>
  </view>
</view> 