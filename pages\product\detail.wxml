<!-- 商品详情页 -->
<scroll-view 
  class="container" 
  scroll-y 
  enable-flex 
  bindscroll="onScroll"
  enhanced
  show-scrollbar="{{false}}"
  bounces="{{true}}"
>
  <!-- 商品主图轮播 -->
  <swiper class="product-images" autoplay circular indicator-dots>
    <swiper-item wx:for="{{product.images}}" wx:key="*this">
      <image src="{{item}}" mode="aspectFill" />
    </swiper-item>
  </swiper>

  <!-- 商品信息区域 -->
  <view class="info-section">
    <view class="product-name">{{product.name}}</view>
    <view class="price-area">
      <view class="price-row">
        <text class="final-price">¥{{finalPrice}}</text>
        <text class="original-price" wx:if="{{finalPrice < product.price}}">原价 ¥{{product.price}}</text>
      </view>
      <text class="sales-info" style="position: relative; left: -23rpx; top: 10rpx">已售{{product.soldCount}}件</text>
    </view>

    <!-- 促销活动 -->
    <view class="promotion-item" wx:if="{{promotions && promotions.length > 0}}" style="height: 45rpx">
      <text class="promotion-label">促销</text>
      <view class="promotion-content">
        <text class="promotion-value">
          <block wx:for="{{promotions}}" wx:key="index">
            <block wx:if="{{item.type === 'reduce' && item.rules.isRepeatable}}">每满<text>{{item.rules.minAmount}}</text>减<text>{{item.rules.discount}}</text></block>
            <block wx:if="{{item.type === 'reduce' && !item.rules.isRepeatable && item.conditions.isNewUser}}">新人首单立减<text>{{item.rules.discount}}</text></block>
            <block wx:if="{{index < promotions.length - 1}}">；</block>
          </block>
        </text>
      </view>
      <text class="arrow"></text>
    </view>

    <button class="share-btn" open-type="share">
      <text class="share-icon"></text>
      <text style="position: relative; left: 138rpx; top: 0rpx">分享</text>
    </button>
  </view>

  <!-- 商品规格 -->
  <view class="spec-section" style="height: 84rpx; display: flex; box-sizing: border-box">
    <text class="spec-label" style="width: 81rpx; height: 48rpx; display: block; box-sizing: border-box">规格</text>
    <view class="spec-options">
      <view 
        class="spec-option {{selectedSpec === '42ml' ? 'active' : ''}}" 
        bindtap="selectSpec" 
        data-spec="42ml"
       style="height: 46rpx; display: block; box-sizing: border-box">
        42ml
      </view>
      <view 
        class="spec-option {{selectedSpec === '84ml' ? 'active' : ''}}" 
        bindtap="selectSpec" 
        data-spec="84ml"
       style="width: 110rpx; height: 48rpx; display: block; box-sizing: border-box">
        84ml
      </view>
    </view>
  </view>

  <!-- 配送方式 -->
  <view class="delivery-item" style="height: 60rpx; display: flex; box-sizing: border-box">
    <text class="delivery-label">配送</text>
    <text class="delivery-value">顺丰包邮</text>
    <text class="arrow"></text>
  </view>

  <!-- 商品参数 -->
  <view class="params-section" wx:if="{{product.params.length > 0}}">
    <view class="section-title">商品参数</view>
    <view class="params-list">
      <view class="param-item" wx:for="{{product.params}}" wx:key="key">
        <text class="param-key">{{item.key}}</text>
        <text class="param-value">{{item.value}}</text>
      </view>
    </view>
  </view>

  <!-- 商品详情区域 -->
  <view class="detail-section">
    <view class="detail-header">
      <text class="main-title">控油·止脱·增发</text>
      <text class="sub-title">3重通路 协同提升发量</text>
    </view>
    
    <view class="feature-list">
      <view class="feature-item">
        <text class="feature-num">①</text>
        <text class="feature-title">3天控油</text>
        <text class="feature-desc">改善头皮油脂分泌</text>
      </view>
      <view class="feature-item">
        <text class="feature-num">②</text>
        <text class="feature-title">1-2周有效止脱</text>
      </view>
      <view class="feature-item">
        <text class="feature-num">③</text>
        <text class="feature-title">4-6周生真发</text>
        <text class="feature-desc">84%受试者头发直径增粗</text>
      </view>
    </view>

    <!-- 商品详情图片 -->
    <block wx:if="{{product.detailImages && product.detailImages.length > 0}}">
      <view class="detail-images">
        <image 
          wx:for="{{product.detailImages}}" 
          wx:key="index" 
          src="{{item}}" 
          mode="widthFix" 
          class="detail-image"
          binderror="onImageError"
          data-type="detail"
          data-index="{{index}}"
          lazy-load="{{true}}"
        />
      </view>
    </block>
    <block wx:elif="{{product.detailImage}}">
      <view class="detail-images">
        <image 
          src="{{product.detailImage}}" 
          mode="widthFix" 
          class="detail-image"
          binderror="onImageError"
          data-type="detail"
          lazy-load="{{true}}"
        />
      </view>
    </block>
    <block wx:else>
      <view class="no-detail-image">暂无详情图片</view>
    </block>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <button class="icon-btn contact-btn" open-type="contact">
      <image src="/images/客服.png" />
    </button>
    <view class="icon-btn cart-btn" bindtap="goToCart">
      <image src="/images/购物车.png" mode="aspectFit"></image>
      <view class="badge" wx:if="{{cartCount > 0}}">{{cartCount}}</view>
    </view>
    <button class="add-cart" bindtap="addToCart" style="position: relative; left: 32rpx; top: -1rpx">加入购物车</button>
    <button class="buy-now" bindtap="buyNow" style="width: 194rpx; display: flex; box-sizing: border-box; left: 0rpx; top: -2rpx; position: relative">立即购买</button>
  </view>
</scroll-view> 