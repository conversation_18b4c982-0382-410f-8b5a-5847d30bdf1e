const db = wx.cloud.database()

// 格式化日期
function formatDate(date) {
  if (!(date instanceof Date)) {
    date = new Date(date);
  }
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

Page({
  data: {
    exchangeCode: '',
    couponList: []
  },

  onLoad() {
    this.getCouponList();
  },

  // 获取U粉券列表
  getCouponList() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || (!userInfo.dbUserId && !userInfo._openid)) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '加载中' });
    const whereCondition = {
      // 开发环境使用 dbUserId，生产环境使用 _openid
      [userInfo.dbUserId ? 'userId' : '_openid']: userInfo.dbUserId || userInfo._openid
    };
    
    db.collection('coupons')
      .where(whereCondition)
      .orderBy('createTime', 'desc')
      .get()
      .then(res => {
        console.log('[优惠券] 列表获取成功:', res.data);
        // 格式化日期
        const formattedList = res.data.map(item => ({
          ...item,
          endDate: formatDate(item.endDate)
        }));
        this.setData({
          couponList: formattedList
        });
      })
      .catch(err => {
        console.error('[优惠券] 获取列表失败:', err);
      })
      .finally(() => {
        // 确保 loading 被关闭
        wx.hideLoading();
      });
  },

  // 输入兑换码
  onInputCode(e) {
    this.setData({
      exchangeCode: e.detail.value
    });
  },

  // 兑换U粉券
  exchangeCoupon() {
    const { exchangeCode } = this.data;
    if (!exchangeCode) {
      return wx.showToast({
        title: '请输入兑换码',
        icon: 'none'
      });
    }

    wx.showLoading({ title: '兑换中' });
    console.log('[优惠券] 开始兑换:', exchangeCode);

    // 调用云函数兑换优惠券
    wx.cloud.callFunction({
      name: 'exchangeCoupon',
      data: {
        code: exchangeCode,
        userId: wx.getStorageSync('userInfo').dbUserId
      }
    })
    .then(res => {
      console.log('[优惠券] 兑换结果:', res);
      if (res.result.success) {
        wx.showToast({
          title: '兑换成功',
          icon: 'success'
        });
        this.setData({ exchangeCode: '' });
        this.getCouponList();
      } else {
        console.log('[优惠券] 兑换失败:', res.result);
        wx.showToast({
          title: res.result.message || '兑换失败',
          icon: 'none'
        });
      }
    })
    .catch(err => {
      console.error('[优惠券] 兑换出错:', err);
      wx.showToast({
        title: '兑换失败',
        icon: 'error'
      });
    })
    .finally(() => {
      // 确保 loading 被关闭
      wx.hideLoading();
    });
  }
}); 