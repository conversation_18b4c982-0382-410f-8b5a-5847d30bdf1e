const validators = {
  isEmail: (email) => {
    const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return re.test(email);
  },

  isPhone: (phone) => {
    const re = /^1[3-9]\d{9}$/;
    return re.test(phone);
  },

  isPassword: (password) => {
    // 至少8位，包含大小写字母和数字
    const re = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/;
    return re.test(password);
  },

  isEmpty: (value) => {
    return value === undefined || value === null || value === '';
  },

  isLength: (value, min, max) => {
    const length = value.toString().length;
    return length >= min && (max === undefined || length <= max);
  }
};

export default validators; 