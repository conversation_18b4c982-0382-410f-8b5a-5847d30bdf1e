# 微信支付云函数

这个云函数基于微信官方的云开发支付模块，提供了一套完整的微信支付解决方案，适用于小程序支付场景。

## 特性

- 基于微信支付云开发模块，简化支付接入流程
- 支持JSAPi支付（小程序支付）
- 支持支付结果查询
- 支持订单关闭
- 支持申请退款和退款查询
- 支持开发环境下模拟支付
- 支持支付回调处理

## 使用方法

### 1. 配置

首先确保`config.json`中包含了必要的API权限：

```json
{
  "permissions": {
    "openapi": [
      "cloudpay.unifiedorder",
      "cloudpay.orderquery",
      "cloudpay.closeorder",
      "cloudpay.refund",
      "cloudpay.refundquery",
      "cloudpay.downloadbill"
    ],
    "clientIP": true
  }
}
```

### 2. 统一下单（创建支付）

```javascript
// 前端代码
wx.cloud.callFunction({
  name: 'wxpayFunctions',
  data: {
    action: 'unifiedOrder',
    orderNo: '商户订单号', // 必填，您的系统内部订单编号
    totalFee: 100,       // 必填，支付金额，单位为分，例如1元就是100
    mockPayment: false,  // 可选，是否使用模拟支付，默认根据环境自动判断
    forceRealPayment: true // 可选，强制使用真实支付，即使在开发环境中
  }
}).then(res => {
  if (res.result.code === 0) {
    // 调用微信支付
    wx.requestPayment({
      ...res.result.data,
      success(payRes) {
        console.log('支付成功', payRes);
        // 处理支付成功逻辑
      },
      fail(payErr) {
        console.error('支付失败', payErr);
        // 处理支付失败逻辑
      }
    });
  } else {
    console.error('获取支付参数失败', res.result);
  }
}).catch(err => {
  console.error('调用支付云函数失败', err);
});
```

### 3. 查询订单

```javascript
// 前端代码
wx.cloud.callFunction({
  name: 'wxpayFunctions',
  data: {
    action: 'queryOrder',
    orderNo: '商户订单号' // 必填
  }
}).then(res => {
  console.log('订单查询结果', res.result);
}).catch(err => {
  console.error('查询订单失败', err);
});
```

### 4. 关闭订单

```javascript
// 前端代码
wx.cloud.callFunction({
  name: 'wxpayFunctions',
  data: {
    action: 'closeOrder',
    orderNo: '商户订单号' // 必填
  }
}).then(res => {
  console.log('关闭订单结果', res.result);
}).catch(err => {
  console.error('关闭订单失败', err);
});
```

### 5. 申请退款

```javascript
// 前端代码
wx.cloud.callFunction({
  name: 'wxpayFunctions',
  data: {
    action: 'refund',
    refundParams: {
      out_trade_no: '商户订单号', // 必填
      out_refund_no: '退款单号',  // 可选，不填时自动生成
      total_fee: 100,           // 必填，订单总金额，单位为分
      refund_fee: 100,          // 必填，退款金额，单位为分
      refund_desc: '退款原因'     // 可选，退款原因
    }
  }
}).then(res => {
  console.log('申请退款结果', res.result);
}).catch(err => {
  console.error('申请退款失败', err);
});
```

### 6. 查询退款

```javascript
// 前端代码
wx.cloud.callFunction({
  name: 'wxpayFunctions',
  data: {
    action: 'queryRefund',
    refundId: '退款单号' // 必填，商户退款单号
  }
}).then(res => {
  console.log('查询退款结果', res.result);
}).catch(err => {
  console.error('查询退款失败', err);
});
```

### 7. 处理支付回调

支付回调处理通过`callback.js`单独实现，您需要在微信支付商户平台设置回调地址，并配置对应的HTTP触发器云函数。

## 环境变量

本云函数支持以下环境变量配置：

- `APPID`：小程序AppID
- `MCHID`：微信支付商户号
- `NOTIFY_URL`：支付结果通知URL
- `APIV3_KEY`：微信支付V3 API密钥（用于回调解密）

如不设置环境变量，将使用代码中的默认值。

## 模拟支付

在开发环境中，默认会启用模拟支付功能，无需真实扣款即可测试支付流程。如需在开发环境使用真实支付，可以设置`forceRealPayment: true`参数。

模拟支付会返回以下特征的支付参数：
- paySign以"MOCK_SIGNATURE_"开头
- package包含"MOCK_PREPAY_ID"
- 增加_mock=true标记

## 注意事项

1. 请确保商户号、AppID等配置正确
2. 生产环境中请设置正确的回调通知地址
3. 请妥善保管APIv3密钥和证书，避免泄露
4. 退款操作需要在商户平台配置退款证书
5. 订单号必须唯一，建议使用时间戳+随机数作为订单号 