<view class="container">
  <view class="coupon-list">
    <view class="coupon-item {{selectedCoupon._id === item._id ? 'selected' : ''}}" 
          wx:for="{{coupons}}" 
          wx:key="_id"
          bindtap="selectCoupon"
          data-index="{{index}}">
      <view class="left">
        <view class="amount">
          <text class="symbol">¥</text>
          <text class="number">{{item.amount}}</text>
        </view>
        <view class="condition">满{{item.minAmount}}可用</view>
      </view>
      <view class="right">
        <view class="coupon-name">{{item.name || '优惠券'}}</view>
        <view class="date">有效期至：{{item.endDate}}</view>
      </view>
    </view>
  </view>

  <view class="empty" wx:if="{{coupons.length === 0}}">
    <text>暂无可用优惠券</text>
  </view>
</view> 