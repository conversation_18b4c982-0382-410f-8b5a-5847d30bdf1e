Page({
  data: {
    showWelcome: false,
    showMessage: false,
    hideHi: false,
    showContent: false,
    showRules: false,
    name: '',
    phone: '',
    gender: '',  // 1: 男, 2: 女
    age: '',
    occupation: '',
    receipt: '',  // 购买凭证图片
    showAgreement: false,  // 控制协议弹窗显示
    agreementImages: [
      'cloud://cloud1-2gfror0a1b871162.636c-cloud1-2gfror0a1b871162-1336684304/协议/协议1.png',
      'cloud://cloud1-2gfror0a1b871162.636c-cloud1-2gfror0a1b871162-1336684304/协议/协议2.png'
    ],
    agreed: false,  // 是否同意协议
  },

  onLoad() {
    // 获取协议图片的临时链接
    this.getAgreementImages();
    // 加载字体
    wx.loadFontFace({
      family: 'FZShouJinShu-Z01S',
      source: 'url("/images/字体/方正手迹-悟空体.TTF")',
      success: (res) => {
        console.log('字体加载成功', res)
        
        // 显示欢迎动画
        this.timer1 = setTimeout(() => {
          this.setData({ showWelcome: true });
        }, 300);

        // 1.5秒后隐藏Hi
        this.timer2 = setTimeout(() => {
          this.setData({ hideHi: true });
        }, 1500);

        // 2.3秒后显示第二行文字
        this.timer3 = setTimeout(() => {
          this.setData({ showMessage: true });
        }, 2300);

        // 4秒后显示主页面
        this.timer4 = setTimeout(() => {
          this.setData({ 
            showWelcome: false,
            showContent: true 
          });
        }, 4000);
      },
      fail: (err) => {
        console.log('字体加载失败', err);
        this.setData({ 
          showContent: true 
        });
      }
    })
  },

  // 获取协议图片临时链接
  async getAgreementImages() {
    try {
      const { fileList } = await wx.cloud.getTempFileURL({
        fileList: this.data.agreementImages
      });
      
      this.setData({
        agreementImages: fileList.map(file => file.tempFileURL)
      });
    } catch (err) {
      console.error('获取协议图片失败：', err);
    }
  },

  // 点击任意位置立即进入主页面
  enterMainPage() {
    if (!this.data.showWelcome) return;
    
    // 清除所有定时器
    for (let i = 1; i <= 4; i++) {
      clearTimeout(this['timer' + i]);
    }
    
    // 同时设置两个状态
    this.setData({ 
      showWelcome: false,
      showContent: true
    });
    // 延迟显示规则内容
    setTimeout(() => {
      this.setData({
        showRules: true
      });
    }, 300);
  },

  // 处理姓名输入
  onNameInput(e) {
    console.log('姓名输入：', e.detail.value);  // 添加日志
    this.setData({
      name: e.detail.value
    });
  },

  // 处理手机号输入
  onPhoneInput(e) {
    console.log('手机号输入：', e.detail.value);  // 添加日志
    this.setData({
      phone: e.detail.value
    });
  },

  // 选择图片
  chooseImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      camera: 'back',
      success: (res) => {
        console.log('选择图片成功：', res);
        this.setData({
          receipt: res.tempFiles[0].tempFilePath
        });
      },
      fail: (err) => {
        console.log('选择图片失败：', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 删除图片
  deleteImage() {
    this.setData({
      receipt: ''
    });
  },

  // 切换协议勾选状态
  toggleAgreement() {
    // 如果未同意，显示协议
    if (!this.data.agreed) {
      this.showAgreementModal();
    } else {
      this.setData({ agreed: false });
    }
  },

  // 显示协议内容
  showAgreementModal() {
    this.setData({
      showAgreement: true
    });
  },

  // 选择性别
  selectGender(e) {
    const gender = parseInt(e.currentTarget.dataset.gender);
    this.setData({ gender });
  },

  // 处理年龄输入
  onAgeInput(e) {
    let age = e.detail.value;
    // 限制年龄范围在 1-120
    if (age > 120) age = 120;
    if (age < 1) age = 1;
    this.setData({ age });
  },

  // 处理职业输入
  onOccupationInput(e) {
    this.setData({
      occupation: e.detail.value
    });
  },

  // 修改提交表单方法
  async submitForm() {
    if (!this.data.agreed) {
      wx.showToast({
        title: '请阅读UW产品体验官招募协议',
        icon: 'none'
      });
      return;
    }

    // 开始表单验证和提交流程
    const { name, phone, receipt, gender, age, occupation } = this.data;
    
    // 表单验证
    if (!name.trim()) {
      return wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
    }
    
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      return wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
    }

    if (!gender) {
      return wx.showToast({
        title: '请选择性别',
        icon: 'none'
      });
    }

    if (!age) {
      return wx.showToast({
        title: '请填写年龄',
        icon: 'none'
      });
    }

    if (!occupation) {
      return wx.showToast({
        title: '请填写职业',
        icon: 'none'
      });
    }
    
    if (!receipt) {
      return wx.showToast({
        title: '请上传购买凭证',
        icon: 'none'
      });
    }

    wx.showLoading({
      title: '提交中...'
    });

    try {
      // 1. 上传图片到云存储
      const cloudPath = `trial_receipts/${Date.now()}-${Math.random().toString(36).substr(2)}.png`;
      const uploadRes = await wx.cloud.uploadFile({
        cloudPath,
        filePath: receipt,
      });

      // 2. 获取用户openid
      const app = getApp();
      const openid = app.globalData.openid;
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      // 3. 添加记录到云数据库
      const db = wx.cloud.database();
      await db.collection('trial_applications').add({
        data: {
          _openid: openid,  // 用户openid
          name,
          phone,
          gender,
          age: parseInt(age),
          occupation,
          receiptImage: uploadRes.fileID,
          status: 'pending',  // 申请状态：pending-待审核，approved-已通过，rejected-已拒绝
          createTime: db.serverDate(),  // 创建时间
          updateTime: db.serverDate(),  // 更新时间
          feedback: [],  // 反馈记录
          checkInDays: 0,  // 打卡天数
          lastCheckIn: null,  // 最后打卡时间
          isActive: true,  // 是否活跃
          refundStatus: 'pending',  // 退款状态：pending-未退款，processing-退款中，completed-已退款
          productInfo: {
            name: 'UW强韧健发精华液',
            spec: '42ml'
          }
        }
      });

      wx.hideLoading();
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      });

      // 4. 提交成功后跳转到体验记录页面
      setTimeout(() => {
        wx.redirectTo({
          url: '/pages/member/trial-record/index'
        });
      }, 1500);

    } catch (err) {
      console.error('提交失败：', err);
      wx.hideLoading();
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
    }
  },

  // 同意协议
  confirmSubmit() {
    this.setData({
      agreed: true,
      showAgreement: false
    });
  },

  // 关闭协议弹窗
  closeAgreement() {
    this.setData({
      showAgreement: false
    });
  },

  // 阻止滑动穿透
  preventScroll(e) {
    // 允许modal-body内部滚动
    if (e.target.className === 'modal-body' || e.target.className.includes('agreement-image')) {
      return;
    }
    // 阻止其他区域滚动
    e.preventDefault();
  },

  stopPropagation() {
    // 阻止事件冒泡
    return;
  }
}) 