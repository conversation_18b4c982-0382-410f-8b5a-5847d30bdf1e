const paymentService = {
  // 调用支付
  async requestPayment(orderNo, totalFee, openid, useMockPayment = false) {
    try {
      // 确保金额有效
      if (totalFee === undefined || totalFee === null || isNaN(totalFee) || totalFee <= 0) {
        throw new Error('支付金额必须大于0');
      }
      
      // 确保订单号有效
      if (!orderNo) {
        throw new Error('订单号不能为空');
      }
      
      // 确保totalFee是整数
      const total = Math.floor(Number(totalFee));
      
      // 记录环境信息，但不做任何处理
      const accountInfo = wx.getAccountInfoSync();
      const envVersion = accountInfo?.miniProgram?.envVersion;
      
      console.log('[支付v3] 发起支付请求:', {
        订单号: orderNo,
        金额: total,
        openid: openid || '通过云函数获取',
        环境: envVersion,
        强制真实支付: true
      });

      // 调用统一下单，无视useMockPayment参数
      const callData = {
        action: 'unifiedOrder',
        orderNo,
        totalFee: total,
        openid
      };
      
      const res = await wx.cloud.callFunction({
        name: 'pay2',
        data: callData
      });

      console.log('[支付v3] 云函数返回结果:', res);

      if (!res.result || res.result.code !== 0) {
        const errorMsg = res.result?.message || '获取支付参数失败';
        console.error('[支付v3] 统一下单失败:', errorMsg, res);
        throw new Error(errorMsg);
      }

      // 记录支付参数
      console.log('[支付v3] 支付参数:', res.result.data);

      // 构建标准微信支付参数
      const payParams = {
        timeStamp: String(res.result.data.timeStamp),
        nonceStr: String(res.result.data.nonceStr),
        package: String(res.result.data.package),
        signType: 'RSA', // APIv3使用RSA签名
        paySign: String(res.result.data.paySign)
      };

      // 记录最终的支付参数
      console.log('[支付v3] 最终支付参数:', payParams);
      console.log('[支付v3] 强制使用真实支付API');

      // 强制使用真实支付API，无视模拟支付参数
      return new Promise((resolve, reject) => {
        wx.requestPayment({
          ...payParams,
          success: (res) => {
            console.log('[支付v3] 支付成功:', res);
            resolve(res);
          },
          fail: (err) => {
            console.error('[支付v3] 支付失败:', {
              错误: err.errMsg || err.message,
              支付参数: payParams
            });
            reject(err);
          }
        });
      });
    } catch (err) {
      console.error('[支付v3] 支付失败:', {
        错误信息: err.message || err.errMsg || '未知错误',
        错误码: err.code || '无',
        订单号: orderNo,
        时间: new Date().toISOString()
      });
      throw err;
    }
  },

  // 测试支付参数
  async testPaymentParams(orderNo, totalFee, useMockPayment = false) {
    try {
      // 验证参数
      if (!orderNo) {
        throw new Error('订单号不能为空');
      }
      
      if (totalFee === undefined || totalFee === null || isNaN(totalFee) || totalFee <= 0) {
        throw new Error('支付金额必须大于0');
      }
      
      // 确保totalFee是整数
      const total = Math.floor(Number(totalFee));
      
      console.log('[支付v3] 测试支付参数:', {
        订单号: orderNo,
        金额: total,
        使用模拟支付: useMockPayment ? '是' : '否'
      });
      
      // 调用云函数获取支付参数
      const res = await wx.cloud.callFunction({
        name: 'pay2',
        data: {
          action: 'unifiedOrder',
          orderNo,
          totalFee: total,
          // 只有在明确指定useMockPayment=true时才传递mockPayment参数
          ...(useMockPayment ? { mockPayment: true } : {})
        }
      });

      console.log('[支付v3] 测试支付参数结果:', res);

      if (!res.result || res.result.code !== 0) {
        const errorMsg = res.result?.message || '获取支付参数失败';
        console.error('[支付v3] 测试支付参数失败:', errorMsg, res);
        throw new Error(errorMsg);
      }

      return res.result.data;
    } catch (err) {
      console.error('[支付v3] 测试支付参数失败:', {
        错误信息: err.message || err.errMsg || '未知错误',
        错误码: err.code || '无',
        订单号: orderNo,
        时间: new Date().toISOString()
      });
      throw err;
    }
  },

  // 查询支付结果
  async queryPayment(outTradeNo, transactionId) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'pay2',
        data: {
          action: 'queryOrder',
          outTradeNo,
          transactionId
        }
      });

      if (res.result.code !== 0) {
        throw new Error(res.result.message || '查询支付结果失败');
      }

      return res.result.data;
    } catch (err) {
      console.error('[支付v3] 查询失败:', {
        错误: err.message,
        订单号: outTradeNo,
        交易号: transactionId,
        时间: new Date().toISOString()
      });
      throw err;
    }
  },

  // 关闭订单
  async closeOrder(outTradeNo) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'pay2',
        data: {
          action: 'closeOrder',
          outTradeNo
        }
      });

      if (res.result.code !== 0) {
        throw new Error(res.result.message || '关闭订单失败');
      }

      return res.result.data;
    } catch (err) {
      console.error('[支付v3] 关闭订单失败:', {
        错误: err.message,
        订单号: outTradeNo,
        时间: new Date().toISOString()
      });
      throw err;
    }
  },

  // 申请退款
  async refund(refundParams) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'pay2',
        data: {
          action: 'refund',
          refundParams
        }
      });

      if (res.result.code !== 0) {
        throw new Error(res.result.message || '申请退款失败');
      }

      return res.result.data;
    } catch (err) {
      console.error('[支付v3] 申请退款失败:', {
        错误: err.message,
        订单号: refundParams.out_trade_no,
        时间: new Date().toISOString()
      });
      throw err;
    }
  },

  // 查询退款
  async queryRefund(outRefundNo) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'pay2',
        data: {
          action: 'queryRefund',
          refundId: outRefundNo
        }
      });

      if (res.result.code !== 0) {
        throw new Error(res.result.message || '查询退款失败');
      }

      return res.result.data;
    } catch (err) {
      console.error('[支付v3] 查询退款失败:', {
        错误: err.message,
        退款单号: outRefundNo,
        时间: new Date().toISOString()
      });
      throw err;
    }
  },

  // 下载对账单
  async downloadBill(billDate, billType = 'ALL') {
    try {
      const res = await wx.cloud.callFunction({
        name: 'pay2',
        data: {
          action: 'downloadBill',
          billDate,
          billType
        }
      });

      if (res.result.code !== 0) {
        throw new Error(res.result.message || '下载对账单失败');
      }

      return res.result.data;
    } catch (err) {
      console.error('[支付v3] 下载对账单失败:', {
        错误: err.message,
        日期: billDate,
        时间: new Date().toISOString()
      });
      throw err;
    }
  },

  // 验证支付参数
  validatePayParams(params) {
    return !!(
      params &&
      params.timeStamp &&
      params.nonceStr &&
      params.package &&
      params.signType &&
      params.paySign
    );
  }
};

export default paymentService; 