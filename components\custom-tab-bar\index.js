Component({
  /**
   * 组件的属性列表
   */
  properties: {},

  /**
   * 组件的初始数据
   */
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#000000",
    list: [
      {
        pagePath: "/pages/index/index",
        text: "品牌中心",
        iconPath: "/images/品牌中心-未选中.png",
        selectedIconPath: "/images/品牌中心-选中.png"
      },
      {
        pagePath: "/pages/shop/shop",
        text: "品牌商城",
        iconPath: "/images/品牌商城-未选中.png",
        selectedIconPath: "/images/品牌商城-选中.png"
      },
      {
        pagePath: "/pages/profile/profile",
        text: "个人中心",
        iconPath: "/images/个人中心-未选中.png",
        selectedIconPath: "/images/个人中心-选中.png"
      }
    ]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      
      wx.switchTab({
        url,
        success: () => {
          this.setData({
            selected: data.index
          });
        }
      });
    }
  }
}) 