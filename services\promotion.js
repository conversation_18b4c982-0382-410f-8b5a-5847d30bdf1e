import request from '../utils/request';

const db = wx.cloud.database();
const _ = db.command;

const promotionService = {
  // 获取当前进行中的促销活动
  getActivePromotions: async function() {
    try {
      const now = db.serverDate();
      return await db.collection('promotions')
        .where({
          status: 1,
          startTime: _.lte(now),
          endTime: _.gte(now),
          isDeleted: _.or([false, _.exists(false)])
        })
        .orderBy('priority', 'desc')
        .get();
    } catch (err) {
      console.error('[促销] 获取活动列表失败:', {
        错误: err.message,
        时间: new Date().toISOString()
      });
      throw err;
    }
  },

  // 获取商品的促销信息
  getProductPromotions: async function(productId) {
    try {
      console.log('[促销] 开始查询商品关联活动, 商品ID:', productId);
      
      // 先查询商品关联的促销活动
      const relations = await db.collection('promotion_products')
        .where({
          productId: productId
        })
        .get();
      
      console.log('[促销] 商品关联数据:', relations);
      
      if (relations.data.length === 0) {
        console.log('[促销] 未找到商品关联的促销活动');
        return { data: [] };
      }

      const now = db.serverDate();
      const promotionIds = relations.data.map(r => r.promotionId);
      console.log('[促销] 查询促销活动IDs:', promotionIds);
      
      // 先只查询活动ID，不加其他条件
      const promotions = await db.collection('promotions')
        .where({
          _id: _.in(promotionIds)
        })
        .get();
      
      console.log('[促销] 活动数据详情:', promotions.data.map(p => ({
        id: p._id,
        name: p.name,
        type: p.type,
        rules: p.rules,
        conditions: p.conditions
      })));
      
      // 检查每个活动的状态和时间
      promotions.data.forEach(promo => {
        console.log('[促销] 活动详情:', {
          id: promo._id,
          status: promo.status,
          startTime: new Date(promo.startTime),
          endTime: new Date(promo.endTime),
          now: new Date()
        });
      });

      console.log('[促销] 查询到的促销活动:', promotions);
      return promotions;
    } catch (err) {
      console.error('[促销] 获取商品促销信息失败:', err);
      throw err;
    }
  },

  // 计算商品促销价格
  calculatePromotionPrice: async function(originalPrice, promotions) {
    console.log('[促销服务] 开始计算促销价格:', {
      原价: originalPrice,
      促销活动: promotions
    });
    
    // 确保价格是有效数字
    originalPrice = parseFloat(originalPrice) || 0;
    let finalPrice = originalPrice;
    let discountDetails = [];

    // 如果没有促销活动，直接返回原价
    if (!Array.isArray(promotions) || promotions.length === 0) {
      console.log('[促销服务] 无促销活动，返回原价');
      return {
        finalPrice,
        discountDetails
      };
    }

    try {
      // 获取用户信息，检查是否是新用户
      const userInfo = wx.getStorageSync('userInfo');
      const isNewUser = userInfo ? await this.checkNewUserEligibility(userInfo.dbUserId) : false;
      console.log('[促销服务] 用户资格检查:', {
        是否新用户: isNewUser
      });

      // 遍历所有促销活动
      for (const promotion of promotions) {
        console.log('[促销服务] 处理促销活动:', promotion);
        
        // 确保promotion是有效对象
        if (!promotion || typeof promotion !== 'object') {
          console.log('[促销服务] 跳过无效促销活动');
          continue;
        }

        switch (promotion.type) {
          case 'reduce': {
            // 检查是否是新用户专享
            if (promotion.conditions && promotion.conditions.isNewUser) {
              if (!isNewUser) {
                console.log('[促销服务] 非新用户，跳过新用户优惠');
                continue;
              }
              const discount = parseFloat(promotion.rules.discount) || 0;
              console.log('[促销服务] 新用户优惠金额:', discount);
              finalPrice -= discount;
              discountDetails.push({
                name: '新人首单立减',
                discount: discount,
                times: 1
              });
            } else if (promotion.rules.isRepeatable) {
              // 处理可重复使用的满减
              const threshold = parseFloat(promotion.rules.minAmount) || 0;
              const discount = parseFloat(promotion.rules.discount) || 0;
              const times = Math.floor(finalPrice / threshold);
              console.log('[促销服务] 满减优惠计算:', {
                门槛: threshold,
                优惠: discount,
                可用次数: times
              });
              if (times > 0) {
                const totalDiscount = discount * times;
                finalPrice -= totalDiscount;
                discountDetails.push({
                  name: `满${threshold}减${discount}`,
                  discount: discount,
                  times: times
                });
              }
            } else {
              // 处理普通满减
              const threshold = parseFloat(promotion.rules.minAmount) || 0;
              const discount = parseFloat(promotion.rules.discount) || 0;
              if (finalPrice >= threshold) {
                console.log('[促销服务] 普通满减优惠:', {
                  门槛: threshold,
                  优惠: discount
                });
                finalPrice -= discount;
                discountDetails.push({
                  name: `满${threshold}减${discount}`,
                  discount: discount,
                  times: 1
                });
              }
            }
            break;
          }
          // ... 其他促销类型
        }
      }

      // 确保最终价格是有效数字
      finalPrice = Math.max(0, parseFloat(finalPrice.toFixed(2)) || 0);

      console.log('[促销服务] 促销计算结果:', {
        最终价格: finalPrice,
        优惠详情: discountDetails
      });

      return {
        finalPrice,
        discountDetails
      };
    } catch (err) {
      console.error('[促销服务] 计算促销价格失败:', {
        错误: err.message,
        错误类型: err.name,
        错误堆栈: err.stack,
        时间: new Date().toISOString()
      });
      throw err;
    }
  },

  // 检查用户是否可以参与活动
  checkUserEligibility: async function(userId, promotionId) {
    try {
      const promotion = await db.collection('promotions')
        .doc(promotionId)
        .get();

      if (!promotion.data.limitPerUser) {
        return true;
      }

      // 查询用户已参与次数
      const orders = await db.collection('orders')
        .where({
          userId: userId,
          'promotions.promotionId': promotionId,
          status: _.in([1, 2, 3]) // 已支付、已发货、已完成的订单
        })
        .count();

      return orders.total < promotion.data.limitPerUser;
    } catch (err) {
      console.error('[促销] 检查用户资格失败:', {
        错误: err.message,
        用户ID: userId,
        活动ID: promotionId,
        时间: new Date().toISOString()
      });
      throw err;
    }
  },

  // 获取用户可用的促销活动
  getUserPromotions: async function(userId) {
    try {
      const now = db.serverDate();
      const promotions = await db.collection('promotions')
        .where({
          status: 1,
          startTime: _.lte(now),
          endTime: _.gte(now),
          isDeleted: _.or([false, _.exists(false)])
        })
        .orderBy('priority', 'desc')
        .get();

      // 检查每个活动的用户资格
      const eligiblePromotions = [];
      for (const promotion of promotions.data) {
        const isEligible = await this.checkUserEligibility(userId, promotion._id);
        if (isEligible) {
          eligiblePromotions.push(promotion);
        }
      }

      return eligiblePromotions;
    } catch (err) {
      console.error('[促销] 获取用户可用活动失败:', {
        错误: err.message,
        用户ID: userId,
        时间: new Date().toISOString()
      });
      throw err;
    }
  },

  // 检查是否是新用户首单
  checkNewUserEligibility: async function(userId) {
    try {
      // 查询用户订单数
      const orders = await db.collection('orders')
        .where({
          userId: userId,
          status: _.in([1, 2, 3]) // 已支付、已发货、已完成的订单
        })
        .count();
      
      return orders.total === 0;  // 如果没有完成的订单，则是新用户首单
    } catch (err) {
      console.error('[促销] 检查新用户资格失败:', err);
      throw err;
    }
  }
};

export default promotionService; 