import request from '../utils/request';
import logger from '../utils/logger';

const API = {
  CREATE_PAYMENT: '/api/payment/create',
  QUERY_PAYMENT: '/api/payment/query'
};

const paymentService = {
  // 创建支付订单
  createPayment: async function(orderData) {
    try {
      // 确保金额有效
      if (!orderData.totalAmount || isNaN(orderData.totalAmount) || orderData.totalAmount <= 0) {
        throw new Error('无效的订单金额');
      }
      
      // 计算支付金额（分）
      const totalFee = Math.floor(orderData.totalAmount * 100);
      
      // 支付参数准备
      console.log('[支付服务] 准备支付参数:', {
        订单号: orderData.orderNo,
        金额: totalFee,
        模拟支付: orderData.mockPayment === true ? '是' : '否',
        强制真实支付: orderData.forceRealPayment === true ? '是' : '否'
      });
      
      // 判断是否为开发环境 - 仅用于日志记录，不影响支付逻辑
      const accountInfo = wx.getAccountInfoSync();
      const envVersion = accountInfo?.miniProgram?.envVersion;
      
      console.log('[支付服务] 环境信息:', {
        环境: envVersion
      });
      
      // 获取用户信息
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo || !userInfo.dbUserId) {
        throw new Error('请先登录');
      }
      
      // 调用统一下单 - 可以通过参数控制是否使用模拟支付
      const callData = {
        action: 'unifiedOrder',
        orderNo: orderData.orderNo,
        totalFee: totalFee,
        userId: userInfo.dbUserId,
        _openid: userInfo._openid
      };
      
      // 如果明确需要使用模拟支付，则添加参数
      if (orderData.mockPayment === true) {
        callData.mockPayment = true;
      }
      
      // 如果明确需要强制使用真实支付，则添加参数
      if (orderData.forceRealPayment === true) {
        callData.forceRealPayment = true;
      }
      
      // 记录完整的请求参数
      console.log('[支付服务] 发起统一下单请求:', callData);
      
      const res = await wx.cloud.callFunction({
        name: 'pay2',
        data: callData
      });
      
      console.log('[支付服务] 统一下单返回:', res);

      if (!res.result || res.result.code !== 0) {
        throw new Error(res.result?.message || '创建支付订单失败');
      }

      // 确保支付参数符合标准格式
      const paymentParams = {
        timeStamp: String(res.result.data.timeStamp),
        nonceStr: String(res.result.data.nonceStr),
        package: String(res.result.data.package),
        signType: String(res.result.data.signType || 'RSA'),
        paySign: String(res.result.data.paySign)
      };
      
      // 保留total_fee参数
      if (res.result.data.total_fee) {
        paymentParams.total_fee = res.result.data.total_fee;
      } else {
        // 如果云函数没有返回total_fee，我们自己添加一个
        paymentParams.total_fee = totalFee;
      }
      
      // 如果是模拟支付，记录标记
      if (res.result.data._mock === true) {
        paymentParams._mock = true;
      }

      // 记录最终生成的支付参数
      console.log('[支付服务] 生成的支付参数:', {
        ...paymentParams,
        paySign: '(签名已隐藏)'
      });

      return paymentParams;
    } catch (err) {
      logger.error('支付服务', '创建支付订单失败', err);
      throw err;
    }
  },

  // 查询支付结果
  queryPayment: async function(orderNo) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'pay2',
        data: {
          action: 'queryOrder',  // 添加 action 参数
          orderNo: orderNo
        }
      });

      if (!res.result || res.result.code !== 0) {
        throw new Error(res.result?.message || '查询支付结果失败');
      }

      return res.result.data;
    } catch (err) {
      logger.error('支付服务', '查询支付结果失败', err);
      throw err;
    }
  },

  // 验证支付参数
  validatePayParams(params) {
    const requiredParams = ['timeStamp', 'nonceStr', 'package', 'signType', 'paySign'];
    
    // 检查参数是否存在
    const missingParams = requiredParams.filter(key => !params[key]);
    if (missingParams.length > 0) {
      console.error('缺少支付参数:', missingParams);
      return false;
    }

    // 检查参数类型
    const invalidParams = requiredParams.filter(key => 
      typeof params[key] !== 'string' || !params[key].length
    );
    if (invalidParams.length > 0) {
      console.error('支付参数类型错误:', invalidParams);
      return false;
    }

    return true;
  },

  // 发起支付
  requestPayment: async function(paymentParams) {
    logger.info('支付服务', '开始发起支付', paymentParams);

    if (!this.validatePayParams(paymentParams)) {
      throw new Error('支付参数无效');
    }

    // 记录环境信息，但不做任何处理
    const accountInfo = wx.getAccountInfoSync();
    const envVersion = accountInfo?.miniProgram?.envVersion;
    
    console.log('[支付服务] 当前环境:', envVersion);
    console.log('[支付服务] 强制使用真实支付API');
    
    // 检查是否包含total_fee参数（通常在模拟支付时会有）
    let totalFee = null;
    if (paymentParams.total_fee) {
      totalFee = paymentParams.total_fee;
      console.log('[支付服务] 检测到total_fee参数:', totalFee);
    }
    
    // 确保所有参数都是字符串类型，并且仅使用微信支付标准参数
    const params = {
      timeStamp: String(paymentParams.timeStamp),
      nonceStr: String(paymentParams.nonceStr),
      package: String(paymentParams.package),
      signType: String(paymentParams.signType),
      paySign: String(paymentParams.paySign)
    };
    
    // 验证package参数是否符合格式要求
    if (!params.package.startsWith('prepay_id=')) {
      logger.error('支付服务', 'package参数格式错误', {
        当前package: params.package,
        正确格式: 'prepay_id=xxx'
      });
      throw new Error('支付参数错误: package格式不正确，应为prepay_id=xxx');
    }

    // 记录支付参数 - 只包含标准参数
    console.log('[支付服务] 发起微信支付请求:', {
      timeStamp: params.timeStamp,
      nonceStr: params.nonceStr,
      package: params.package,
      signType: params.signType,
      paySign: params.paySign ? '(签名已生成)' : '(签名为空)',
      ...(totalFee ? { total_fee: totalFee } : {})  // 如果有total_fee，也记录下来
    });

    try {
      return new Promise((resolve, reject) => {
        wx.requestPayment({
          ...params,
          success: (res) => {
            logger.info('支付服务', '支付成功', res);
            console.log('[支付服务] 微信支付成功:', res);
            resolve(res);
          },
          fail: (err) => {
            // 使用更新后的logger接口记录错误
            logger.error('支付服务', '支付失败', err);
            
            // 额外记录详细的支付参数信息
            console.error('[支付服务] 微信支付失败:', {
              错误: err.errMsg || err.message,
              详情: err,
              请求参数: {
                timeStamp: params.timeStamp,
                nonceStr: params.nonceStr,
                package: params.package,
                signType: params.signType,
                ...(totalFee ? { total_fee: totalFee } : {})  // 如果有total_fee，也记录下来
              }
            });
            reject(err);
          }
        });
      });
    } catch (err) {
      logger.error('支付服务', '支付请求异常', {
        错误信息: err.message || err.errMsg,
        堆栈: err.stack
      });
      throw err;
    }
  },

  // 查询支付结果
  queryPaymentResult: async function(orderId) {
    try {
      return await request.get(`${API.QUERY_PAYMENT}/${orderId}`);
    } catch (error) {
      console.error('查询支付结果失败:', error);
      throw error;
    }
  },
};

export default paymentService; 