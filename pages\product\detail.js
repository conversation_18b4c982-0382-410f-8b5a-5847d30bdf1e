const db = wx.cloud.database()
import paymentService from '../../services/payment';
import promotionService from '../../services/promotion';
const ENV = require('../../config/env');
const { getCloudPath, getDefaultImage } = require('../../utils/image');

Page({
  data: {
    // 商品详情数据
    product: null,
    // 当前选择的规格
    selectedSpec: '42ml', // 默认选中42ml
    // 购买数量
    quantity: 1,
    promotions: [],
    finalPrice: 0,
    isScrollUp: true,
    lastScrollTop: 0,
    cartCount: 0,
    discountDetails: [],
    currentPrice: null,
    detailImages: [],
  },

  async onLoad(options) {
    console.log('[商品详情] 页面加载, 参数:', options);
    
    try {
      // 如果是从分享链接进入
      if (options.isShare) {
        console.log('[商品详情] 来自分享链接');
        await this.initShareEnv();
      }
      
      const productId = options.id || options.productId;
      if (!productId) {
        throw new Error('商品ID不存在');
      }
      
      // 从数据库加载商品详情
      await this.loadProductDetail(productId);

      // 获取购物车数量
      await this.updateCartCount();
      
      // 更新购物车徽标
      this.updateCartBadge();
      
    } catch (err) {
      console.error('[商品详情] 页面加载失败:', err);
      wx.showToast({
        title: err.message || '加载失败',
        icon: 'error'
      });
      setTimeout(() => wx.navigateBack(), 1500);
    }
  },

  // 初始化商品数据
  async initProductData(product) {
    console.log('[商品详情] 初始化商品数据');
    
    // 验证商品数据
    if (!product.price || isNaN(parseFloat(product.price))) {
      throw new Error('商品价格无效');
    }
    
    // 设置固定销量为"999+"
    product.soldCount = "999+";
    
    // 处理规格价格
    if (product.specs && product.specs.length > 0) {
      product.specs.forEach((spec, index) => {
        if (spec.options) {
          spec.options.forEach((option, optIndex) => {
            if (!option.price || isNaN(parseFloat(option.price))) {
              console.warn(`[商品] 规格价格无效: 规格${index+1}-选项${optIndex+1}`);
              option.price = product.price;
            }
          });
        }
      });
    }
    
    try {
      // 获取促销信息
      const promotionsRes = await promotionService.getProductPromotions(product._id);
      const promotions = promotionsRes.data || [];
      
      // 计算促销价格
      const priceInfo = await promotionService.calculatePromotionPrice(
        product.price,
        promotions
      );
      
      // 更新页面数据
      this.setData({
        product,
        selectedSpec: product.specs?.[0]?.options?.[0]?.name || '默认规格',
        promotions,
        finalPrice: priceInfo.finalPrice,
        discountDetails: priceInfo.discountDetails,
        currentPrice: product.price
      });
    } catch (err) {
      console.error('[商品详情] 加载促销信息失败:', err);
      // 如果促销信息加载失败，仍然显示商品基本信息
      this.setData({
        product,
        selectedSpec: product.specs?.[0]?.options?.[0]?.name || '默认规格',
        currentPrice: product.price,
        finalPrice: product.price
      });
    }
  },

  // 从数据库加载商品详情
  async loadProductDetail(productId) {
    console.log('[商品详情] 开始加载商品:', productId);
    
    wx.showLoading({ title: '加载中' });
    try {
      const res = await db.collection('products').doc(productId).get();
      const product = res.data;
      
      console.log('[商品详情] 原始商品数据:', product);
      
      // 确保所有图片路径都是完整的云存储路径
      if (product.images && Array.isArray(product.images)) {
        product.images = product.images.map(img => {
          if (!img) return getDefaultImage(null);
          if (!img.startsWith('cloud://')) {
            return getCloudPath(img, 'PRODUCT');
          }
          return img;
        }).filter(img => img); // 过滤空值
      } else {
        product.images = [ENV.DEFAULT_IMAGES.PRODUCT];
      }
      
      // 处理详情图数组
      console.log('[商品详情] 处理详情图数组，原始数据:', product.detailImages);
      if (product.detailImages && Array.isArray(product.detailImages)) {
        // 筛选有效的详情图片并转换路径
        product.detailImages = product.detailImages
          .filter(img => img && typeof img === 'string')
          .map(img => getCloudPath(img, 'DETAIL'));
      } else {
        product.detailImages = [];
      }

      // 如果存在老版本的单张详情图，并且详情图数组为空，将单张图添加到数组中
      if (product.detailImage && typeof product.detailImage === 'string' && product.detailImages.length === 0) {
        const detailImagePath = getCloudPath(product.detailImage, 'DETAIL');
        if (detailImagePath) {
          product.detailImages.push(detailImagePath);
        }
      }

      console.log('[商品详情] 详情图数组处理后:', product.detailImages);
      
      if (!product.images || product.images.length === 0) {
        product.images = [ENV.DEFAULT_IMAGES.PRODUCT];
      }
      
      console.log('[商品详情] 处理后的商品数据:', product);
      
      await this.initProductData(product);
    } catch (err) {
      console.error('[商品详情] 加载失败:', err);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 初始化分享环境
  async initShareEnv() {
    try {
      console.log('[商品详情] 开始初始化分享环境');
      
      // 检查是否已登录
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo) {
        // 显示登录提示
        wx.showModal({
          title: '提示',
          content: '登录后可享受更多功能',
          confirmText: '去登录',
          cancelText: '继续浏览',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/login/index'
              });
            }
          }
        });
      }
      
      console.log('[商品详情] 分享环境初始化完成');
    } catch (err) {
      console.error('[商品详情] 初始化分享环境失败:', err);
    }
  },

  // 计算最终价格
  async calculateFinalPrice(basePrice) {
    try {
      console.log('[商品] 开始计算最终价格:', {
        基础价格: basePrice,
        商品ID: this.data.product._id
      });
      
      // 获取促销信息
      const promotionsRes = await promotionService.getProductPromotions(this.data.product._id);
      const promotions = promotionsRes.data || [];
      
      // 计算促销价格
      const priceInfo = await promotionService.calculatePromotionPrice(
        basePrice,
        promotions
      );

      console.log('[商品] 价格计算结果:', {
        基础价格: basePrice,
        促销后价格: priceInfo.finalPrice,
        优惠明细: priceInfo.discountDetails
      });

      this.setData({
        finalPrice: priceInfo.finalPrice,
        discountDetails: priceInfo.discountDetails,
        promotions // 保存促销信息，用于下单时传递
      });
    } catch (err) {
      console.error('[商品] 计算价格失败:', err);
      // 如果计算失败，使用基础价格
      this.setData({
        finalPrice: basePrice,
        discountDetails: []
      });
    }
  },

  // 显示规格选择器
  showSpecSelector() {
    const { product, selectedSpec } = this.data;
    if (!product.specs || product.specs.length === 0) return;

    wx.showActionSheet({
      itemList: product.specs[0].options.map(option => option.name),
      success: (res) => {
        if (res.tapIndex > -1) {
          this.selectSpec({
            currentTarget: {
              dataset: {
                spec: product.specs[0].options[res.tapIndex]
              }
            }
          });
        }
      }
    });
  },

  // 选择规格
  selectSpec(e) {
    const spec = e.currentTarget.dataset.spec;
    console.log('[商品] 选择规格:', spec);

    // 获取规格价格
    let price;
    if (typeof spec === 'string') {
      // 如果是字符串形式的规格名称
      if (spec === '84ml') {
        price = this.data.product.price * 1.8; // 84ml价格是42ml的1.8倍
      } else {
        price = this.data.product.price; // 42ml使用原价
      }
    } else if (spec && spec.price) {
      // 如果是规格对象
      price = parseFloat(spec.price);
    } else {
      console.error('[商品] 无效的规格数据:', spec);
      return;
    }

    if (isNaN(price)) {
      console.error('[商品] 计算规格价格失败:', {
        规格: spec,
        价格: price
      });
      return;
    }

    console.log('[商品] 规格价格计算:', {
      规格: spec,
      原价: this.data.product.price,
      规格价格: price
    });

    this.setData({
      selectedSpec: typeof spec === 'string' ? spec : spec.name,
      currentPrice: price // 保存当前规格的基础价格
    });

    // 重新计算促销价格
    this.calculateFinalPrice(price);
  },

  // 检查规格选择
  checkSpecSelected() {
    if (this.data.product.specs.length > 0 && !this.data.selectedSpec) {
      wx.showToast({
        title: '请选择规格',
        icon: 'none'
      });
      return false;
    }
    return true;
  },

  // 修改数量
  changeQuantity(e) {
    const { type } = e.currentTarget.dataset
    let { quantity } = this.data
    if (type === 'minus' && quantity > 1) {
      quantity--
    } else if (type === 'plus' && quantity < this.data.product.stock) {
      quantity++
    }
    this.setData({ quantity })
  },

  // 加入购物车
  async addToCart() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.dbUserId) {
      wx.showToast({ title: '请先登录', icon: 'none' });
      return;
    }

    // 检查商品数据
    if (!this.data.product || !this.data.product._id) {
      wx.showToast({ title: '商品数据错误', icon: 'none' });
      return;
    }

    // 检查规格选择
    const specName = this.data.selectedSpec || '默认规格';
    const specPrice = this.data.currentPrice || this.data.product.price;

    const cartData = {
      userId: userInfo.dbUserId,
      product: {
        _id: this.data.product._id,
        name: this.data.product.name,
        image: this.data.product.mainImage || this.data.product.images[0]
      },
      specs: {
        name: specName,
        price: specPrice
      },
      quantity: this.data.quantity,
      selected: false,
      isDeleted: false,
      createTime: db.serverDate()
    };

    console.log('[购物车] 添加商品:', cartData);

    try {
      await db.collection('cart').add({
        data: cartData
      });
      
      wx.showToast({
        title: '已加入购物车',
        icon: 'success'
      });
      
      // 更新购物车数量
      this.updateCartCount();
    } catch (err) {
      console.error('[购物车] 添加失败:', err);
      wx.showToast({
        title: '添加失败',
        icon: 'error'
      });
    }
  },

  // 立即购买
  buyNow() {
    if (!this.checkLoginAndSpecs()) return;
    
    const { product, selectedSpec, quantity, currentPrice } = this.data;
    
    const orderProduct = {
      _id: product._id,
      name: product.name,
      price: currentPrice || product.price,
      image: product.mainImage || product.images[0],
      quantity: quantity,
      specs: {
        name: selectedSpec,
        price: currentPrice || product.price
      }
    };

    wx.navigateTo({
      url: '/pages/order/confirm',
      success: function(res) {
        res.eventChannel.emit('acceptDataFromOpenerPage', {
          products: [orderProduct]
        });
      }
    });
  },

  // 检查登录和规格选择
  checkLoginAndSpecs() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({ url: '/pages/profile/profile' });
          }
        }
      });
      return false;
    }

    const { product, selectedSpec } = this.data;
    if (product.specs.length > 0 && !selectedSpec) {
      wx.showToast({
        title: '请选择规格',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 监听滚动事件
  onScroll(e) {
    const scrollTop = e.detail.scrollTop;
    const isScrollUp = scrollTop < this.data.lastScrollTop;
    
    this.setData({
      isScrollUp,
      lastScrollTop: scrollTop
    });
  },

  // 图片加载错误处理
  onImageError(e) {
    console.log('[商品详情] 图片加载失败:', e);
    const type = e.currentTarget.dataset.type;
    const index = e.currentTarget.dataset.index;
    
    if (type === 'detail') {
      // 处理详情图片加载错误
      if (index !== undefined) {
        // 详情图数组的错误处理
        const detailImages = this.data.product.detailImages;
        if (detailImages && detailImages[index]) {
          // 替换为本地默认图片
          detailImages[index] = '/images/placeholder.png';
          this.setData({
            'product.detailImages': detailImages
          });
        }
      } else {
        // 单张详情图的错误处理
        this.setData({
          'product.detailImage': '/images/placeholder.png'
        });
      }
    } else {
      // 商品主图的错误处理
      const images = this.data.product.images;
      const imageIndex = e.currentTarget.dataset.index || 0;
      
      if (images && images[imageIndex]) {
        images[imageIndex] = '/images/placeholder.png';
        this.setData({
          'product.images': images
        });
      }
    }
  },

  // 分享功能
  onShareAppMessage() {
    const { product } = this.data;
    if (!product) return;
    
    const shareParams = {
      id: product._id,
      isShare: true
    };
    
    console.log('[商品详情] 生成分享参数:', shareParams);
    
    return {
      title: product.name,
      path: '/pages/product/detail?' + Object.entries(shareParams)
        .map(([key, value]) => `${key}=${value}`)
        .join('&'),
      imageUrl: product.mainImage || product.image
    };
  },

  // 跳转到购物车
  goToCart() {
    wx.navigateTo({
      url: '/pages/cart/index?from=product',
      success: () => {
        // 跳转成功后的回调
        console.log('跳转购物车成功');
      },
      fail: (err) => {
        console.error('跳转购物车失败:', err);
        wx.showToast({
          title: '跳转失败，请重试',
          icon: 'none',
          duration: 1500
        });
      }
    });
  },

  // 获取购物车数量用于显示徽标
  getCartCount() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo._openid) return;

    db.collection('cart')
      .where({
        _openid: userInfo._openid
      })
      .count()
      .then(res => {
        // 通知页面更新徽标
        const app = getApp();
        if (app.globalData.updateCartBadge) {
          app.globalData.updateCartBadge(res.total);
        }
      });
  },

  // 更新购物车徽标
  updateCartBadge() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo._openid) return;

    db.collection('cart')
      .where({
        _openid: userInfo._openid
      })
      .count()
      .then(res => {
        // 更新当前页面的徽标
        this.setData({
          cartCount: res.total
        });

        // 更新全局徽标
        const app = getApp();
        if (app.globalData.updateCartBadge) {
          app.globalData.updateCartBadge(res.total);
        }
      })
      .catch(err => {
        console.error('[购物车] 获取数量失败:', err);
      });
  },

  // 创建订单
  createOrder() {
    console.log('[订单] 开始创建订单');
    const { product, selectedSpec, quantity } = this.data;
    const userInfo = wx.getStorageSync('userInfo');
    
    if (!userInfo || !userInfo.dbUserId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    const orderData = {
      userId: userInfo.dbUserId,
      products: [{
        id: product._id,
        name: product.name,
        price: product.price,
        quantity: quantity,
        spec: selectedSpec
      }],
      totalAmount: product.price * quantity,
      status: 0, // 待支付
      createTime: db.serverDate()
    };

    console.log('[数据库] 准备创建订单:', {
      ...orderData,
      createTime: '服务器时间'
    });

    db.collection('orders')
      .add({
        data: orderData
      })
      .then(res => {
        console.log('[数据库] 订单创建成功:', {
          orderId: res._id,
          time: new Date().toISOString()
        });
        
        // 跳转到订单确认页
        wx.navigateTo({
          url: `/pages/order/confirm?id=${res._id}`
        });
      })
      .catch(err => {
        console.error('[数据库] 订单创建失败:', {
          operation: 'add',
          collection: 'orders',
          error: err,
          time: new Date().toISOString()
        });
        wx.showToast({
          title: '创建订单失败',
          icon: 'error'
        });
      });
  },

  // 更新价格
  updatePrice() {
    const { product, selectedSpec } = this.data;
    let basePrice = product.price;
    
    // 根据规格调整基础价格
    if (selectedSpec === '84ml') {
      basePrice = basePrice * 1.8; // 84ml价格是42ml的1.8倍
    }
    
    // 计算促销后价格
    const finalPrice = promotionService.calculatePromotionPrice(
      basePrice,
      this.data.promotions
    );
    
    this.setData({ finalPrice });
  },

  // 更新购物车数量
  async updateCartCount() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo || !userInfo.dbUserId) return;
      
      const res = await db.collection('cart')
        .where({
          userId: userInfo.dbUserId,
          isDeleted: false
        })
        .count();

      this.setData({
        cartCount: res.total
      });
    } catch (err) {
      console.error('[商品详情] 获取购物车数量失败:', err);
    }
  },

  onShow() {
    // 获取购物车数量
    this.updateCartCount();

    // 页面显示时更新购物车徽标
    this.updateCartBadge();
  },

  // 监听页面事件
  onPageScroll(e) {
    // ... 其他代码保持不变
  },

  processData: function(data) {
    let result = { ...data };
    
    // 处理详情图片
    if (result.detailImages && Array.isArray(result.detailImages)) {
      // 过滤有效的详情图片
      result.detailImages = result.detailImages.filter(img => img && typeof img === 'string' && img.trim() !== '');
    } else if (result.detail_image && typeof result.detail_image === 'string' && result.detail_image.trim() !== '') {
      // 兼容处理单张详情图片
      result.detailImages = [result.detail_image];
    } else {
      result.detailImages = [];
    }
    
    // 处理详情图片路径
    result.detailImages = result.detailImages.map(img => {
      // 如果是相对路径，转换为云存储路径
      if (img.startsWith('/') || img.startsWith('./')) {
        return `cloud://cloud1-2gfror0a1b871162.products/detail/${img.replace(/^[\.\/]+/, '')}`;
      } 
      // 如果已经是云存储路径但不包含环境ID，添加环境ID
      else if (img.startsWith('cloud://') && !img.includes('cloud1-2gfror0a1b871162')) {
        return img.replace(/cloud:\/\/[^\.]+/, 'cloud://cloud1-2gfror0a1b871162');
      }
      // 其它情况保持原样
      return img;
    });
    
    console.log('处理后的详情图片:', result.detailImages);
    
    // ... existing code ...
    return result;
  },

  onImageError: function(e) {
    // ... existing code ...
    
    // 处理详情图片加载失败
    if (e.currentTarget.dataset.type === 'detail') {
      const index = e.currentTarget.dataset.index;
      const detailImages = [...this.data.detailImages];
      detailImages[index] = '/images/placeholder.png'; // 使用默认图片
      this.setData({ detailImages });
      console.error('详情图片加载失败:', e);
    }
  },
}) 