{"description": "项目配置文件", "packOptions": {"ignore": [{"value": ".giti<PERSON>re", "type": "file"}, {"value": "backend", "type": "folder"}, {"value": "/minitest", "type": "folder"}], "include": []}, "setting": {"bundle": false, "userConfirmedBundleSwitch": false, "urlCheck": true, "scopeDataCheck": false, "coverView": true, "es6": true, "postcss": true, "compileHotReLoad": true, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": true, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": true, "nodeModules": false, "enhance": true, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "showES6CompileOption": false, "ignoreUploadUnusedFiles": true, "useCompilerPlugins": [], "compileWorklet": false, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "3.7.8", "appid": "wxf57755081128c53d", "projectname": "UW小程序", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "miniprogramRoot": "", "cloudfunctionRoot": "cloudfunctions/", "cloudfunctionTemplateRoot": "cloudfunctionTemplate/", "srcMiniprogramRoot": "", "cloudbaseRoot": "{{ENV.CLOUD_ENV}}/", "cloudfunctionRootConfig": {"envId": "{{ENV.CLOUD_ENV}}"}, "testRoot": "minitest/", "simulatorPluginLibVersion": {}}