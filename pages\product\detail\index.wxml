<view class="container">
  <!-- 商品图片 -->
  <view class="product-images">
    <image wx:for="{{product.images}}" wx:key="*this" 
      src="{{item}}" 
      mode="widthFix" />
  </view>

  <!-- 商品信息 -->
  <view class="product-info">
    <view class="name">{{product.name}}</view>
    <view class="price">¥{{currentPrice}}</view>
    <view class="desc">{{product.description}}</view>
  </view>

  <!-- 规格选择 -->
  <view class="specs-section">
    <view class="section-title">规格</view>
    <view class="specs-list">
      <view class="spec-item {{selectedSpec === '42ml' ? 'active' : ''}}" 
            bindtap="selectSpec" 
            data-spec="42ml">
        <text class="spec-size">42ml</text>
        <text class="spec-price">¥248</text>
      </view>
      <view class="spec-item {{selectedSpec === '84ml' ? 'active' : ''}}" 
            bindtap="selectSpec" 
            data-spec="84ml">
        <text class="spec-size">84ml</text>
        <text class="spec-price">¥398</text>
      </view>
    </view>
  </view>

  <!-- 数量选择 -->
  <view class="quantity-section">
    <view class="section-title">数量</view>
    <view class="quantity-control">
      <text class="minus" bindtap="changeQuantity" data-type="minus">-</text>
      <text class="number">{{quantity}}</text>
      <text class="plus" bindtap="changeQuantity" data-type="plus">+</text>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar {{isScrollUp ? '' : 'hidden'}}">
    <view class="cart-btn" bindtap="addToCart">加入购物车</view>
    <view class="buy-btn" bindtap="buyNow">立即购买</view>
  </view>
</view> 