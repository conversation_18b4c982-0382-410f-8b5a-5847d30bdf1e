const BASE_URL = 'https://api.yourserver.com';

const request = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${BASE_URL}${url}`,
      ...options,
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else {
          reject(res);
        }
      },
      fail: reject
    });
  });
};

export const api = {
  // 用户相关
  login: (data) => request('/api/login', {
    method: 'POST',
    data
  }),
  
  // 商品相关
  getProducts: () => request('/api/products', {
    method: 'GET'
  }),
  
  // 订单相关
  createOrder: (data) => request('/api/orders', {
    method: 'POST',
    data
  })
}; 