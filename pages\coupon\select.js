const db = wx.cloud.database();

Page({
  data: {
    totalAmount: 0,
    coupons: [],
    selectedCoupon: null
  },

  onLoad(options) {
    if (options.amount) {
      const totalAmount = parseFloat(options.amount);
      this.setData({ totalAmount });
      this.loadCoupons(totalAmount);
    }
  },

  // 加载可用优惠券
  async loadCoupons(totalAmount) {
    console.log('[优惠券选择] 开始加载优惠券:', {
      订单金额: totalAmount
    });

    wx.showLoading({ title: '加载中' });
    try {
      const userInfo = wx.getStorageSync('userInfo');
      console.log('[优惠券选择] 用户信息:', {
        用户ID: userInfo.dbUserId
      });

      const res = await db.collection('coupons')
        .where({
          userId: userInfo.dbUserId,
          isUsed: 'false',
          minAmount: db.command.lte(totalAmount),
          endDate: db.command.gte(db.serverDate())
        })
        .orderBy('amount', 'desc')
        .get();

      console.log('[优惠券选择] 查询结果:', {
        优惠券数量: res.data.length,
        查询条件: {
          用户ID: userInfo.dbUserId,
          最小金额: totalAmount,
          当前时间: new Date()
        }
      });

      // 格式化优惠券数据
      const coupons = res.data.map(coupon => ({
        ...coupon,
        endDate: this.formatDate(coupon.endDate)
      }));

      console.log('[优惠券选择] 格式化后的优惠券:', coupons);

      this.setData({ coupons });
    } catch (err) {
      console.error('[优惠券选择] 加载失败:', err);
      wx.showToast({
        title: '加载优惠券失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 选择优惠券
  selectCoupon(e) {
    const { index } = e.currentTarget.dataset;
    const coupon = this.data.coupons[index];
    
    console.log('[优惠券选择] 选择优惠券:', {
      索引: index,
      优惠券信息: coupon
    });

    // 获取页面实例
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];

    console.log('[优惠券选择] 准备回调上一页面');
    
    // 调用上一页面的回调
    if (prevPage && prevPage.selectCouponCallback) {
      prevPage.selectCouponCallback(coupon);
      console.log('[优惠券选择] 回调完成');
    } else {
      console.warn('[优惠券选择] 未找到回调函数');
    }

    wx.navigateBack();
  },

  // 不使用优惠券
  cancelSelect() {
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];
    
    if (prevPage && prevPage.selectCouponCallback) {
      prevPage.selectCouponCallback(null);
    }
    
    wx.navigateBack();
  },

  formatDate(date) {
    date = new Date(date);
    return `${date.getFullYear()}.${String(date.getMonth() + 1).padStart(2, '0')}.${String(date.getDate()).padStart(2, '0')}`;
  }
}); 