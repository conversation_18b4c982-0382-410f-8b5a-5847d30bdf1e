<view class="container">
  <!-- 订单状态 -->
  <view class="status-section">
    <view class="status-icon">
      <image src="/images/{{orderStatus.icon}}" mode="aspectFit"/>
    </view>
    <view class="status-text">{{orderStatus.text}}</view>
  </view>

  <!-- 商品信息 -->
  <view class="product-section">
    <view class="product-item" wx:for="{{order.products}}" wx:key="_id">
      <image class="product-thumb" src="{{item.image}}" mode="aspectFill"/>
      <view class="product-info">
        <!-- 左侧商品信息 -->
        <view class="left-info">
          <view class="product-name">{{item.name}}</view>
          <view class="product-spec" wx:if="{{item.specs || item.spec}}">
            {{item.specs.name || item.specs || item.spec}}
          </view>
        </view>
        <!-- 右侧价格和数量 -->
        <view class="right-info">
          <view class="price">¥{{item.price}}</view>
          <view class="quantity">x{{item.quantity}}</view>
        </view>
      </view>
    </view>
  </view>
</view> 