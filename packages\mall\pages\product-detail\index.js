// packages/mall/pages/product-detail/index.js
Page({
  data: {
    product: null,
    loading: false
  },

  onLoad: function(options) {
    // 简单展示静态数据
    this.setData({
      product: {
        name: '商品名称',
        price: 199,
        description: '商品描述信息'
      }
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.id) {
      this.loadProductDetail(options.id);
    } else {
      wx.showToast({
        title: '商品ID无效',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载商品详情
  async loadProductDetail(productId) {
    this.setData({ loading: true });
    
    try {
      const db = wx.cloud.database();
      const res = await db.collection('products').doc(productId).get();
      
      if (res.data) {
        // 简单修复方案：忽略云存储图片，直接使用本地图片
        res.data.images = ['/images/logo.png'];
        
        // 设置商品数据
        this.setData({
          product: res.data,
          loading: false
        });
        
        console.log('商品详情加载成功，使用本地图片替代');
      } else {
        throw new Error('商品不存在');
      }
    } catch (err) {
      console.error('加载商品详情失败:', err);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 图片加载错误处理
   */
  onImageError(e) {
    const index = e.currentTarget.dataset.index;
    let product = this.data.product;
    
    // 使用已存在的图片作为默认图片
    const defaultImage = '/images/logo.png';
    
    // 更新对应索引的图片为默认图片
    if (product.images && product.images.length > index) {
      product.images[index] = defaultImage;
      
      this.setData({
        product: product
      });
      
      console.log('图片加载失败，已替换为默认图片', index);
    }
  }
})