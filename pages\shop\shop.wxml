<view class="container">
  <!-- 商品列表 -->
  <block wx:if="{{productList.length > 0}}">
    <view class="product-grid">
      <view class="product-item" 
            wx:for="{{productList}}" 
            wx:key="_id" 
            bindtap="goToDetail" 
            data-id="{{item._id}}">
        <common-image 
          src="{{item.mainImage}}" 
          mode="aspectFill" 
          custom-class="product-image"
          image-type="PRODUCT"
          bind:imageerror="onImageError"
          data-index="{{index}}"
        />
        <view class="product-info">
          <view class="product-name">{{item.shortName || item.name}}</view>
          <view class="price-info">
            <text class="price">¥{{item.price}}</text>
            <text class="original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
          </view>
        </view>
      </view>
    </view>
  </block>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <common-image 
      src="/images/empty-shop.png" 
      mode="aspectFit" 
      custom-class="empty-image"
      image-type="PLACEHOLDER"
    />
    <view class="empty-text">暂无商品</view>
    <view class="empty-subtext">商品正在上架中，请稍后再来~</view>
    <button class="refresh-btn" bindtap="getProductList">刷新</button>
  </view>

  <!-- 悬浮购物车按钮 -->
  <view class="cart-btn" bindtap="goToCart">
    <common-image 
      src="/images/购物车.png" 
      mode="aspectFit"
      custom-class="cart-icon"
      image-type="PLACEHOLDER"
    />
  </view>
</view>


