// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command
const $ = db.command.aggregate

// 订单状态枚举
const OrderStatus = {
  PENDING_PAY: 0,    // 待支付
  PAID: 1,          // 已支付
  SHIPPED: 2,       // 已发货
  COMPLETED: 3,     // 已完成
  CANCELLED: 4,     // 已取消
  REFUNDING: 5,     // 退款中
  REFUNDED: 6       // 已退款
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  console.log(`执行订单功能: ${action}`, data)

  // 根据action参数执行不同操作
  switch (action) {
    case 'createOrder':
      return await createOrder(data, openid)
    case 'cancelOrder':
      return await cancelOrder(data, openid)
    case 'getOrderList':
      return await getOrderList(data, openid)
    case 'getOrderDetail':
      return await getOrderDetail(data, openid)
    case 'payOrder':
      return await payOrder(data, openid)
    default:
      return {
        code: -1,
        msg: '未知的操作类型'
      }
  }
}

// 创建订单
async function createOrder(data, openid) {
  const transaction = await db.startTransaction()
  
  try {
    // 检查订单集合是否存在
    try {
      await db.createCollection('orders')
      console.log('创建orders集合成功')
    } catch (err) {
      console.log('orders集合已存在')
    }

    const { 
      items, 
      address, 
      couponId = null, 
      remark = '', 
      totalAmount,
      freight = 0 
    } = data

    // 验证购物车商品库存
    const stockCheckResult = await cloud.callFunction({
      name: 'tradeFunction',
      data: {
        action: 'checkStock',
        data: { items }
      }
    })

    if (!stockCheckResult.result.data.isAllAvailable) {
      return {
        code: -1,
        msg: '部分商品库存不足',
        data: stockCheckResult.result.data.items
      }
    }

    // 生成订单号
    const orderNo = generateOrderNo()

    // 创建订单
    const orderData = {
      orderNo,
      items,
      address,
      couponId,
      remark,
      totalAmount,
      freight,
      finalAmount: totalAmount + freight,
      status: 0, // 0:待支付, 1:已支付, 2:已发货, 3:已完成, -1:已取消
      _openid: openid,
      createTime: db.serverDate(),
      updateTime: db.serverDate(),
      paymentTime: null,
      shipmentTime: null,
      completionTime: null,
      cancelTime: null
    }

    // 添加订单
    const orderRes = await db.collection('orders').add({
      data: orderData
    })

    // 更新库存并清空购物车项
    for (const item of items) {
      // 更新商品库存
      await db.collection('products').doc(item.productId).update({
        data: {
          stock: _.inc(-item.quantity),
          updateTime: db.serverDate()
        }
      })

      // 如果存在购物车ID，删除购物车项
      if (item.cartId) {
        await cloud.callFunction({
          name: 'tradeFunction',
          data: {
            action: 'removeCartItem',
            data: { cartId: item.cartId }
          }
        })
      }
    }

    return {
      code: 0,
      msg: '订单创建成功',
      data: {
        orderId: orderRes._id,
        orderNo
      }
    }
  } catch (error) {
    // 回滚事务
    await transaction.rollback()
    
    console.error('创建订单失败:', error)
    return {
      code: -1,
      msg: '创建订单失败',
      error: error.message
    }
  }
}

// 取消订单
async function cancelOrder(data, openid) {
  const transaction = await db.startTransaction()
  
  try {
    const { orderId } = data

    // 查询订单
    const orderRes = await db.collection('orders').doc(orderId).get()
    
    if (!orderRes.data) {
      return {
        code: -1,
        msg: '订单不存在'
      }
    }

    // 检查权限
    if (orderRes.data._openid !== openid) {
      return {
        code: -1,
        msg: '无权限取消此订单'
      }
    }

    // 检查订单状态
    if (orderRes.data.status !== 0) {
      return {
        code: -1,
        msg: '只有待支付订单可以取消'
      }
    }

    // 更新订单状态
    await db.collection('orders').doc(orderId).update({
      data: {
        status: -1,
        cancelTime: db.serverDate(),
        updateTime: db.serverDate()
      }
    })

    // 恢复库存
    for (const item of orderRes.data.items) {
      await db.collection('products').doc(item.productId).update({
        data: {
          stock: _.inc(item.quantity),
          updateTime: db.serverDate()
        }
      })
    }

    return {
      code: 0,
      msg: '订单取消成功'
    }
  } catch (error) {
    // 回滚事务
    await transaction.rollback()
    
    console.error('取消订单失败:', error)
    return {
      code: -1,
      msg: '取消订单失败',
      error: error.message
    }
  }
}

// 获取订单列表
async function getOrderList(data, openid) {
  try {
    const { status, page = 1, pageSize = 10 } = data
    const skip = (page - 1) * pageSize

    // 构建查询条件
    const condition = { _openid: openid }
    if (status !== undefined && status !== null) {
      condition.status = status
    }

    // 查询订单
    const orderRes = await db.collection('orders')
      .where(condition)
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get()

    // 获取总数
    const countRes = await db.collection('orders')
      .where(condition)
      .count()

    return {
      code: 0,
      msg: '获取订单列表成功',
      data: {
        total: countRes.total,
        list: orderRes.data,
        page,
        pageSize
      }
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    return {
      code: -1,
      msg: '获取订单列表失败',
      error: error.message
    }
  }
}

// 获取订单详情
async function getOrderDetail(data, openid) {
  try {
    const { orderId } = data

    // 查询订单
    const orderRes = await db.collection('orders').doc(orderId).get()
    
    if (!orderRes.data) {
      return {
        code: -1,
        msg: '订单不存在'
      }
    }

    // 检查权限
    if (orderRes.data._openid !== openid) {
      return {
        code: -1,
        msg: '无权限查看此订单'
      }
    }

    return {
      code: 0,
      msg: '获取订单详情成功',
      data: orderRes.data
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    return {
      code: -1,
      msg: '获取订单详情失败',
      error: error.message
    }
  }
}

// 支付订单(模拟支付)
async function payOrder(data, openid) {
  try {
    const { orderId } = data

    // 查询订单
    const orderRes = await db.collection('orders').doc(orderId).get()
    
    if (!orderRes.data) {
      return {
        code: -1,
        msg: '订单不存在'
      }
    }

    // 检查权限
    if (orderRes.data._openid !== openid) {
      return {
        code: -1,
        msg: '无权限支付此订单'
      }
    }

    // 检查订单状态
    if (orderRes.data.status !== 0) {
      return {
        code: -1,
        msg: '只有待支付订单可以支付'
      }
    }

    // 更新订单状态
    await db.collection('orders').doc(orderId).update({
      data: {
        status: 1,
        paymentTime: db.serverDate(),
        updateTime: db.serverDate()
      }
    })

    return {
      code: 0,
      msg: '支付成功'
    }
  } catch (error) {
    console.error('支付订单失败:', error)
    return {
      code: -1,
      msg: '支付失败',
      error: error.message
    }
  }
}

// 生成订单号
function generateOrderNo() {
  const now = new Date()
  const year = now.getFullYear().toString().slice(2)
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  const seconds = now.getSeconds().toString().padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  
  return `UW${year}${month}${day}${hours}${minutes}${seconds}${random}`
} 