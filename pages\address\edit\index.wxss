.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: #fff;
  box-sizing: border-box;
  display: block;
}

.save-btn {
  background: #ff4d4f;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  box-sizing: border-box;
  margin: 0 auto;
  width: 90%;
}

/* 按钮点击效果 */
.save-btn:active {
  opacity: 0.8;
  transform: translateY(2rpx);
}

/* 为底部按钮留出空间 */
page {
  padding-bottom: calc(128rpx + env(safe-area-inset-bottom));
} 