import { observable, action } from 'mobx-miniprogram';

export const store = observable({
  // 用户状态
  userInfo: null,
  isLoggedIn: false,

  // 购物车状态
  cartItems: [],
  cartTotal: 0,

  // Actions
  updateUserInfo: action(function(info) {
    this.userInfo = info;
    this.isLoggedIn = !!info;
  }),

  addToCart: action(function(product) {
    const existItem = this.cartItems.find(item => item.id === product.id);
    if (existItem) {
      existItem.quantity += 1;
    } else {
      this.cartItems.push({
        ...product,
        quantity: 1
      });
    }
    this.updateCartTotal();
  }),

  updateCartTotal: action(function() {
    this.cartTotal = this.cartItems.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);
  })
}); 