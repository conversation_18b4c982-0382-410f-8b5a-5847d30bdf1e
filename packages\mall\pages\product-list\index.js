const ENV = require('../../../../config/env');

Page({
  data: {
    productList: [],
    loading: true
  },

  onLoad(options) {
    console.log('[商品列表] 页面加载, 参数:', options);
    this.loadProductList();
  },

  async loadProductList() {
    this.setData({ loading: true });
    
    try {
      // 模拟商品数据
      const mockProducts = [
        {
          _id: '1',
          name: '商品1',
          price: 99.00,
          image: '/images/logo.png'
        },
        {
          _id: '2',
          name: '商品2', 
          price: 199.00,
          image: '/images/logo.png'
        }
      ];
      
      this.setData({
        productList: mockProducts,
        loading: false
      });
      
    } catch (err) {
      console.error('[商品列表] 加载失败:', err);
      this.setData({ loading: false });
    }
  },

  goToDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/packages/mall/pages/product-detail/index?id=${id}`
    });
  }
});
